package net.lingyue.ly.admin.module.website.branch.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分会VO
 */
@Data
public class BranchVO {

    @Schema(description = "分会ID")
    private Long branchId;

    @Schema(description = "分会名称")
    private String branchName;

    @Schema(description = "分会编码")
    private String branchCode;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "Logo图片")
    private String logo;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否禁用")
    private Boolean disabledFlag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}

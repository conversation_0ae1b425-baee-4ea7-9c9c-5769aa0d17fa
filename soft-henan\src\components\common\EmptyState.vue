<template>
  <div class="empty-state">
    <div class="empty-icon">
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm0 44c-11.046 0-20-8.954-20-20s8.954-20 20-20 20 8.954 20 20-8.954 20-20 20z" fill="#d9d9d9"/>
        <path d="M32 20c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm0 20c-4.418 0-8-3.582-8-8s3.582-8 8-8 8 3.582 8 8-3.582 8-8 8z" fill="#d9d9d9"/>
      </svg>
    </div>
    <div class="empty-text">
      <h3>{{ title || '暂无内容' }}</h3>
      <p v-if="description">{{ description }}</p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '暂无内容'
  },
  description: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text h3 {
  font-size: 16px;
  color: #999;
  margin: 0 0 8px 0;
  font-weight: 400;
}

.empty-text p {
  font-size: 14px;
  color: #ccc;
  margin: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .empty-state {
    padding: 40px 20px;
  }
  
  .empty-icon svg {
    width: 48px;
    height: 48px;
  }
  
  .empty-text h3 {
    font-size: 14px;
  }
  
  .empty-text p {
    font-size: 12px;
  }
}
</style> 
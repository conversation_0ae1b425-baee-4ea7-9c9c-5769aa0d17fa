<!--
  * 网站轮播图配置
-->
<template>
  <div class="carousel-config-container">
    <a-spin :spinning="loading">
      <div class="operation-bar">
        <a-button type="primary" @click="handleAddItem">添加轮播图</a-button>
        <a-button type="primary" @click="handleSubmit" style="margin-left: 10px">保存</a-button>
      </div>

      <a-table
        :dataSource="carouselItems"
        :columns="columns"
        :pagination="false"
        :rowKey="(record) => record.title + '_' + record.imageUrl"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'imageUrl'">
            <div class="image-preview">
              <img :src="record.imageUrl" :alt="record.title" v-if="record.imageUrl" />
              <div class="no-image" v-else>无图片</div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'enabled'">
            <a-switch v-model:checked="record.enabled" />
          </template>

          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="handleEditItem(record, index)">编辑</a-button>
            <a-popconfirm
              title="确定要删除这个轮播图吗？"
              @confirm="handleDeleteItem(index)"
              okText="确定"
              cancelText="取消"
            >
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </a-spin>

    <!-- 轮播图编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :maskClosable="false"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入标题" />
        </a-form-item>

        <a-form-item label="图片" name="imageUrl">
          <a-input v-model:value="formData.imageUrl" placeholder="请输入图片URL或上传图片" />
          <div class="upload-wrapper">
            <SmartUpload
              :limit="1"
              :file-type="FILE_FOLDER_TYPE_ENUM.COMMON.value"
              @upload-success="handleImageUploadSuccess"
            />
            <div class="preview-image" v-if="formData.imageUrl">
              <img :src="formData.imageUrl" alt="轮播图预览" />
            </div>
          </div>
        </a-form-item>

        <a-form-item label="链接" name="link">
          <a-input v-model:value="formData.link" placeholder="请输入链接" />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" :max="999" style="width: 100%" />
        </a-form-item>

        <a-form-item label="是否启用" name="enabled">
          <a-switch v-model:checked="formData.enabled" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { websiteConfigApi } from '/@/api/website/website-config-api';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import SmartUpload from '/@/components/framework/smart-upload/index.vue';

const formRef = ref();
const loading = ref(false);
const modalVisible = ref(false);
const modalTitle = ref('添加轮播图');
const editingIndex = ref(-1);

const carouselItems = ref([]);
const formData = ref({
  title: '',
  imageUrl: '',
  link: '',
  sort: 0,
  enabled: true
});

const columns = [
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80
  },
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true
  },
  {
    title: '图片',
    dataIndex: 'imageUrl',
    width: 120
  },
  {
    title: '链接',
    dataIndex: 'link',
    ellipsis: true
  },
  {
    title: '是否启用',
    dataIndex: 'enabled',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150
  }
];

const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  imageUrl: [{ required: true, message: '请上传图片', trigger: 'change' }]
};

// 获取轮播图配置
const fetchConfig = async () => {
  loading.value = true;
  try {
    const res = await websiteConfigApi.getWebsiteCarouselConfig();
    console.log('获取轮播图配置响应:', res);

    if (res.data && res.data.carouselItems) {
      carouselItems.value = res.data.carouselItems.map(item => ({
        title: item.title || '',
        imageUrl: item.imageUrl || '',
        link: item.link || '#',
        sort: item.sort || 0,
        enabled: item.enabled !== false // 默认为true
      }));
    } else {
      carouselItems.value = [];
    }

    console.log('处理后的轮播图数据:', carouselItems.value);
  } catch (error) {
    console.error('获取轮播图配置失败:', error);
    message.error('获取轮播图配置失败');
  } finally {
    loading.value = false;
  }
};

// 处理图片上传成功
const handleImageUploadSuccess = (fileList) => {
  console.log('上传图片成功，返回数据:', fileList);
  if (fileList && fileList.length > 0) {
    const file = fileList[0];
    // 优先使用 fileUrl，如果没有则尝试使用 url
    if (file.fileUrl) {
      formData.value.imageUrl = file.fileUrl;
      console.log('使用 fileUrl:', file.fileUrl);
    } else if (file.url) {
      formData.value.imageUrl = file.url;
      console.log('使用 url:', file.url);
    } else {
      console.error('上传图片成功但未获取到URL:', file);
      message.warning('图片上传成功，但未获取到URL');
      return;
    }

    // 手动触发表单验证
    if (formRef.value) {
      setTimeout(() => {
        formRef.value.validateFields(['imageUrl']);
      }, 100);
    }
  }
};

// 添加轮播图
const handleAddItem = () => {
  modalTitle.value = '添加轮播图';
  editingIndex.value = -1;
  formData.value = {
    title: '',
    imageUrl: '',
    link: '',
    sort: carouselItems.value.length,
    enabled: true
  };
  modalVisible.value = true;
};

// 编辑轮播图
const handleEditItem = (record, index) => {
  modalTitle.value = '编辑轮播图';
  editingIndex.value = index;
  formData.value = { ...record };
  modalVisible.value = true;
};

// 删除轮播图
const handleDeleteItem = (index) => {
  carouselItems.value.splice(index, 1);
  message.success('删除成功');
};

// 弹窗确认
const handleModalOk = () => {
  formRef.value.validate().then(() => {
    if (editingIndex.value === -1) {
      // 添加
      carouselItems.value.push({ ...formData.value });
    } else {
      // 编辑
      carouselItems.value[editingIndex.value] = { ...formData.value };
    }
    modalVisible.value = false;
    message.success(editingIndex.value === -1 ? '添加成功' : '编辑成功');
  });
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 提交表单
const handleSubmit = async () => {
  loading.value = true;
  try {
    // 确保每个轮播图项都有正确的属性
    const validCarouselItems = carouselItems.value.map(item => ({
      title: item.title || '',
      imageUrl: item.imageUrl || '',
      link: item.link || '#',
      sort: item.sort || 0,
      enabled: item.enabled !== false // 默认为true
    }));

    // 按排序字段排序
    validCarouselItems.sort((a, b) => a.sort - b.sort);

    const config = {
      carouselItems: validCarouselItems
    };

    console.log('保存轮播图配置:', JSON.stringify(config));

    const res = await websiteConfigApi.updateWebsiteCarouselConfig(config);
    console.log('保存轮播图配置响应:', res);

    if (res.code === 0) {
      message.success('保存成功');
      // 重新获取配置，确保显示最新数据
      await fetchConfig();
    } else {
      message.error(res.msg || '保存失败');
    }
  } catch (error) {
    console.error('保存轮播图配置失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchConfig();
});

defineExpose({
  fetchConfig
});
</script>

<style lang="less" scoped>
.carousel-config-container {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.image-preview {
  width: 80px;
  height: 45px;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }

  .no-image {
    color: #999;
    font-size: 12px;
  }
}

.upload-wrapper {
  display: flex;
  align-items: center;
}

.preview-image {
  margin-left: 20px;
  width: 160px;
  height: 90px;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>

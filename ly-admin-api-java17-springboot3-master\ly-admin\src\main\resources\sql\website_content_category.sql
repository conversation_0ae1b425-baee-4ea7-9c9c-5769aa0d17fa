-- 内容分类表
CREATE TABLE IF NOT EXISTS `t_website_content_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父分类ID',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `disabled_flag` tinyint(1) DEFAULT '0' COMMENT '是否禁用',
  `deleted_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容分类表';

-- 内容分类关联表
CREATE TABLE IF NOT EXISTS `t_website_content_category_relation` (
  `relation_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `content_id` bigint(20) NOT NULL COMMENT '内容ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`relation_id`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容分类关联表';

-- 为内容表添加分类ID字段（可选，如果使用关联表则不需要）
-- ALTER TABLE `t_website_content` 
-- ADD COLUMN `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID' AFTER `content_type_id`,
-- ADD INDEX `idx_category_id` (`category_id`);

package net.lingyue.ly.admin.module.website.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 网站底部信息配置
 */
@Data
public class WebsiteFooterConfig {

    @Schema(description = "关于我们标题")
    private String aboutTitle;

    @Schema(description = "关于我们内容")
    private String aboutContent;

    @Schema(description = "官方抖音二维码")
    private String douyinQrCode;

    @Schema(description = "官方公众号二维码")
    private String wechatQrCode;

    @Schema(description = "友情链接列表")
    private List<FriendlyLink> friendlyLinks;

    /**
     * 友情链接
     */
    @Data
    public static class FriendlyLink {

        @Schema(description = "名称")
        private String name;

        @Schema(description = "链接URL")
        private String url;

        @Schema(description = "排序")
        private Integer sort;

        @Schema(description = "是否启用")
        private Boolean enabled;
    }
}

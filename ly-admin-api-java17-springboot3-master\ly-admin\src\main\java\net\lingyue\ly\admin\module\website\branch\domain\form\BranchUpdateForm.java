package net.lingyue.ly.admin.module.website.branch.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 分会更新表单
 */
@Data
public class BranchUpdateForm {

    @Schema(description = "分会ID", required = true)
    @NotNull(message = "分会ID不能为空")
    private Long branchId;

    @Schema(description = "分会名称", required = true)
    @NotBlank(message = "分会名称不能为空")
    @Length(max = 100, message = "分会名称最多100字符")
    private String branchName;

    @Schema(description = "分会编码", required = true)
    @NotBlank(message = "分会编码不能为空")
    @Length(max = 50, message = "分会编码最多50字符")
    private String branchCode;

    @Schema(description = "描述")
    @Length(max = 500, message = "描述最多500字符")
    private String description;

    @Schema(description = "Logo图片")
    private String logo;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "是否禁用")
    private Boolean disabledFlag;
}

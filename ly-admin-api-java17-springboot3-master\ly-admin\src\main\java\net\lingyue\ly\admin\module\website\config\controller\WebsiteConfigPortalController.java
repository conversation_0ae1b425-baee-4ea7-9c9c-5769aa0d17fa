package net.lingyue.ly.admin.module.website.config.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteBasicConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteCarouselConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteFooterConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteThemeConfig;
import net.lingyue.ly.admin.module.website.config.service.WebsiteConfigService;
import net.lingyue.ly.base.common.annoation.NoNeedLogin;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 网站配置门户控制器
 */
@RestController
@Tag(name = AdminSwaggerTagConst.Website.WEBSITE_CONFIG)
public class WebsiteConfigPortalController {

    @Resource
    private WebsiteConfigService websiteConfigService;

    @NoNeedLogin
    @Operation(summary = "门户-获取网站基本信息配置")
    @GetMapping("/portal/config/basic")
    public ResponseDTO<WebsiteBasicConfig> getWebsiteBasicConfig() {
        return websiteConfigService.getWebsiteBasicConfig();
    }

    @NoNeedLogin
    @Operation(summary = "门户-获取网站轮播图配置")
    @GetMapping("/portal/config/carousel")
    public ResponseDTO<WebsiteCarouselConfig> getWebsiteCarouselConfig() {
        return websiteConfigService.getWebsiteCarouselConfig();
    }

    @NoNeedLogin
    @Operation(summary = "门户-获取网站底部信息配置")
    @GetMapping("/portal/config/footer")
    public ResponseDTO<WebsiteFooterConfig> getWebsiteFooterConfig() {
        return websiteConfigService.getWebsiteFooterConfig();
    }

    @NoNeedLogin
    @Operation(summary = "门户-获取网站主题颜色配置")
    @GetMapping("/portal/config/theme")
    public ResponseDTO<WebsiteThemeConfig> getWebsiteThemeConfig() {
        return websiteConfigService.getWebsiteThemeConfig();
    }
}

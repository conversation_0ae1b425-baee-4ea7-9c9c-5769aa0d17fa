# 主题颜色配置后端实现说明

## 已完成的后端代码

### 1. 枚举常量
- **文件**：`WebsiteConfigKeyEnum.java`
- **新增**：`WEBSITE_THEME_CONFIG("website_theme_config", "网站主题颜色配置")`

### 2. 数据实体
- **文件**：`WebsiteThemeConfig.java`
- **字段**：
  - `primaryColor`：主色调
  - `primaryDarkColor`：主色调深色
  - `linkColor`：链接颜色
  - `secondaryColor`：辅助色
- **验证**：包含格式验证，确保颜色值为有效的十六进制格式

### 3. 服务层方法
- **文件**：`WebsiteConfigService.java`
- **新增方法**：
  - `getWebsiteThemeConfig()`：获取主题颜色配置
  - `updateWebsiteThemeConfig()`：更新主题颜色配置
- **默认配置**：如果数据库中没有配置，返回红色主题作为默认值

### 4. 管理接口
- **文件**：`WebsiteConfigController.java`
- **新增接口**：
  - `GET /website/config/theme`：获取主题颜色配置
  - `POST /website/config/theme/update`：更新主题颜色配置
- **权限控制**：需要相应的查询和更新权限

### 5. 门户接口
- **文件**：`WebsiteConfigPortalController.java`
- **新增接口**：
  - `GET /portal/config/theme`：门户获取主题颜色配置（无需登录）

## API接口说明

### 管理端接口

#### 获取主题颜色配置
```http
GET /website/config/theme
```

**响应示例**：
```json
{
  "code": 1,
  "data": {
    "primaryColor": "#d32f2f",
    "primaryDarkColor": "#b71c1c", 
    "linkColor": "#d32f2f",
    "secondaryColor": "#ff5722"
  },
  "message": "success"
}
```

#### 更新主题颜色配置
```http
POST /website/config/theme/update
Content-Type: application/json

{
  "primaryColor": "#1976d2",
  "primaryDarkColor": "#1565c0",
  "linkColor": "#1976d2", 
  "secondaryColor": "#2196f3"
}
```

### 门户端接口

#### 获取主题颜色配置
```http
GET /portal/config/theme
```

**特点**：
- 无需登录验证
- 供前端门户网站获取主题配置
- 返回格式与管理端相同

## 数据存储说明

主题颜色配置以JSON格式存储在`website_config`表中：

- `config_key`：`website_theme_config`
- `config_value`：JSON字符串，包含所有颜色配置
- `config_name`：`网站主题颜色配置`

**存储示例**：
```json
{
  "primaryColor": "#d32f2f",
  "primaryDarkColor": "#b71c1c",
  "linkColor": "#d32f2f", 
  "secondaryColor": "#ff5722"
}
```

## 缓存机制

- 使用`ConcurrentHashMap`进行内存缓存
- 配置更新后自动刷新缓存
- 应用启动时自动加载所有配置到缓存

## 错误处理

- 颜色格式验证：确保输入的颜色值符合十六进制格式
- 配置不存在时返回默认配置
- JSON解析异常时记录日志并返回默认配置

## 权限控制

- 查询权限：`website:config:query`
- 更新权限：`website:config:update`
- 门户访问：无需权限验证

## 测试建议

1. **功能测试**：
   - 测试配置的获取和更新
   - 验证默认配置的返回
   - 测试无效颜色值的验证

2. **权限测试**：
   - 验证权限控制是否有效
   - 测试门户接口的无权限访问

3. **性能测试**：
   - 验证缓存机制的效果
   - 测试并发访问的性能

## 注意事项

1. 确保数据库表`website_config`存在
2. 检查权限配置是否正确设置
3. 前端调用时注意接口路径的区别（管理端vs门户端）
4. 颜色值必须以`#`开头的十六进制格式 
package ${packageName};

#foreach ($importClass in $importPackageList)
    $importClass
#end

/**
 * ${basic.description} 更新表单
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@Data
public class ${name.upperCamel}UpdateForm {
    #foreach ($field in $fields)

        #if($field.isEnum)
            ${field.apiModelProperty}
            ${field.checkEnum}
            private $field.javaType $field.fieldName;
        #end
        #if(!$field.isEnum)
            ${field.apiModelProperty}$!{field.notEmpty}$!{field.dict}$!{field.file}
            private $field.javaType $field.fieldName;
        #end
    #end

}

<!--
  * 内容分类管理
-->
<template>
  <a-card :bordered="false">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="addCategory(0)" v-privilege="'website:content:category:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新建分类
        </a-button>
      </div>
    </a-row>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="false"
      rowKey="categoryId"
      size="middle"
      :expandable="{
        defaultExpandAllRows: true,
      }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="addCategory(record.categoryId, record)" v-privilege="'website:content:category:add'">添加子分类</a>
            <a-divider type="vertical" />
            <a @click="editCategory(record)" v-privilege="'website:content:category:update'">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除该分类吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteCategory(record.categoryId)"
              v-privilege="'website:content:category:delete'"
            >
              <a class="smart-table-delete">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
        <template v-else-if="column.dataIndex === 'disabledFlag'">
          <a-tag :color="record.disabledFlag ? 'red' : 'green'">
            {{ record.disabledFlag ? '禁用' : '启用' }}
          </a-tag>
        </template>
      </template>
    </a-table>

    <!-- 分类表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="formData.categoryId ? '编辑分类' : '新建分类'"
      @ok="handleOk"
      @cancel="handleCancel"
      :confirmLoading="confirmLoading"
    >
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="父级分类" v-if="formData.parentId !== 0">
          <a-input v-model:value="parentCategoryName" disabled />
        </a-form-item>
        <a-form-item label="分类名称" name="categoryName">
          <a-input v-model:value="formData.categoryName" placeholder="请输入分类名称" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" :max="9999" style="width: 100%" />
        </a-form-item>
        <a-form-item label="是否禁用">
          <a-switch v-model:checked="formData.disabledFlag" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { contentCategoryApi } from '/@/api/website/content-category-api';

// 表格列定义
const columns = [
  {
    title: '分类名称',
    dataIndex: 'categoryName',
    width: 200,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'disabledFlag',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 表单数据
const formRef = ref();
const modalVisible = ref(false);
const confirmLoading = ref(false);
const parentCategoryName = ref('');

const formData = reactive({
  categoryId: undefined,
  categoryName: '',
  parentId: 0,
  sort: 0,
  disabledFlag: false,
  remark: '',
});

const defaultFormData = {
  categoryId: undefined,
  categoryName: '',
  parentId: 0,
  sort: 0,
  disabledFlag: false,
  remark: '',
};

// 表单验证规则
const rules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { max: 50, message: '分类名称最多50个字符', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
  ],
  remark: [
    { max: 200, message: '备注最多200个字符', trigger: 'blur' },
  ],
};

// 获取分类树
async function getCategoryTree() {
  loading.value = true;
  try {
    const res = await contentCategoryApi.getCategoryTree({});
    if (res.code === 0 && res.data) {
      tableData.value = res.data;
    } else {
      tableData.value = [];
      message.error(res.msg || '获取分类列表失败');
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
    message.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
}

// 添加分类
function addCategory(parentId, parentRecord) {
  Object.assign(formData, defaultFormData);
  formData.parentId = parentId;

  if (parentRecord) {
    parentCategoryName.value = parentRecord.categoryName;
  } else {
    parentCategoryName.value = '顶级分类';
  }

  modalVisible.value = true;
}

// 编辑分类
function editCategory(record) {
  Object.assign(formData, record);

  if (record.parentId === 0) {
    parentCategoryName.value = '顶级分类';
  } else {
    // 查找父级分类名称
    const findParent = (data, parentId) => {
      for (const item of data) {
        if (item.categoryId === parentId) {
          return item.categoryName;
        }
        if (item.children && item.children.length > 0) {
          const name = findParent(item.children, parentId);
          if (name) return name;
        }
      }
      return '';
    };

    parentCategoryName.value = findParent(tableData.value, record.parentId) || '未知分类';
  }

  modalVisible.value = true;
}

// 删除分类
async function deleteCategory(categoryId) {
  try {
    const res = await contentCategoryApi.deleteCategory(categoryId);
    if (res.code === 0) {
      message.success('删除成功');
      getCategoryTree();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (error) {
    console.error('删除分类失败:', error);
    message.error('删除分类失败');
  }
}

// 表单提交
async function handleOk() {
  try {
    confirmLoading.value = true;

    // 表单验证
    await formRef.value.validate();

    // 提交表单
    let res;
    if (formData.categoryId) {
      res = await contentCategoryApi.updateCategory(formData);
    } else {
      res = await contentCategoryApi.addCategory(formData);
    }

    if (res.code === 0) {
      message.success(formData.categoryId ? '更新成功' : '添加成功');
      modalVisible.value = false;
      getCategoryTree();
    } else {
      message.error(res.msg || (formData.categoryId ? '更新失败' : '添加失败'));
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    message.error('表单验证失败，请检查输入');
  } finally {
    confirmLoading.value = false;
  }
}

// 取消表单
function handleCancel() {
  modalVisible.value = false;
}

// 页面加载时获取数据
onMounted(() => {
  getCategoryTree();
});
</script>

<style lang="less" scoped>
.smart-table-delete {
  color: #ff4d4f;
}
</style>

<template>
  <ContentDetail 
    :detail="activityDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'

const route = useRoute()
const loading = ref(false)
const activityDetail = ref({})

// 详情配置
const detailConfig = computed(() => ({
  // 基础字段配置
  idField: 'id',
  titleField: 'title',
  summaryField: 'summary',
  contentField: 'contentHtml',
  attachmentField: 'attachments',
  
  // 元数据字段（顶部显示）
  metaFields: [
    { key: 'time', label: '活动时间', type: 'datetime' },
    { key: 'location', label: '活动地点' },
    { key: 'organizer', label: '主办单位' },
    { key: 'contact', label: '联系方式' }
  ].filter(Boolean), // 过滤掉可能的undefined值
  
  // 信息区域配置（左侧蓝色边框区域）
  infoSection: {
    fields: [
      { key: 'time', label: '活动时间', type: 'datetime' },
      { key: 'location', label: '活动地点' },
      { key: 'organizer', label: '主办单位' },
      { key: 'contact', label: '联系方式' }
    ].filter(Boolean) // 过滤掉可能的undefined值
  },
  
  // 摘要配置
  summaryTitle: '活动简介',
  
  // 其他配置
  showActions: false, // 不显示底部的返回按钮，因为已经有BackToList组件了
  emptyText: '未找到活动信息',
  containerClass: 'activity-detail-container'
}))

// 获取活动详情
const fetchActivityDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    if (!id) {
      message.error('缺少活动ID参数')
      return
    }

    const res = await getContentDetail(id)
    console.log('API response:', res) // 调试信息
    
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      // 转换数据格式以适配ContentDetail组件
      const transformedData = {
        id: res.data.contentId || res.data.id || '',
        title: res.data.title || '无标题',
        time: formatDate(res.data.publishTime || res.data.createTime) || '待定',
        location: res.data.summary || res.data.location || '待定',
        organizer: res.data.organizer || '河南省软组织病研究会',
        contact: res.data.contact || '0371-12345678',
        summary: res.data.summary || '',
        contentHtml: res.data.contentHtml || res.data.description || '',
        attachments: res.data.attachments || []
      }
      
      console.log('Transformed data:', transformedData) // 调试信息
      activityDetail.value = transformedData
    } else {
      message.error('获取活动详情失败')
      // 设置默认数据
      activityDetail.value = {
        id: '',
        title: '获取失败',
        time: '',
        location: '',
        organizer: '',
        contact: '',
        summary: '',
        contentHtml: '',
        attachments: []
      }
    }
  } catch (error) {
    console.error('获取活动详情失败:', error)
    message.error('获取活动详情失败: ' + (error.message || '未知错误'))
    // 设置默认数据
    activityDetail.value = {
      id: '',
      title: '获取失败',
      time: '',
      location: '',
      organizer: '',
      contact: '',
      summary: '',
      contentHtml: '',
      attachments: []
    }
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面加载时获取活动详情
onMounted(() => {
  fetchActivityDetail()
})
</script>

<style scoped>
.activity-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
}
</style>

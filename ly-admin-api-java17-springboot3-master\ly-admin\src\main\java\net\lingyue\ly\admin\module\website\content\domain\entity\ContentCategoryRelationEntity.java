package net.lingyue.ly.admin.module.website.content.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容分类关联实体
 */
@Data
@TableName("t_website_content_category_relation")
public class ContentCategoryRelationEntity {

    @TableId(type = IdType.AUTO)
    private Long relationId;

    /**
     * 内容ID
     */
    private Long contentId;

    /**
     * 分类ID
     */
    private Long categoryId;

    private LocalDateTime createTime;
}

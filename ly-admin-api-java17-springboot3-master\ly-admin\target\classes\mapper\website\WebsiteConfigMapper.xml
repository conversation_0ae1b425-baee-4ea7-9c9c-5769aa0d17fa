<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.website.config.dao.WebsiteConfigDao">

    <select id="queryByPage" resultType="net.lingyue.ly.admin.module.website.config.domain.entity.WebsiteConfigEntity">
        SELECT
            *
        FROM
            t_website_config
        <where>
            <if test="query.configKey != null and query.configKey != ''">
                AND config_key LIKE CONCAT('%', #{query.configKey}, '%')
            </if>
            <if test="query.configName != null and query.configName != ''">
                AND config_name LIKE CONCAT('%', #{query.configName}, '%')
            </if>
        </where>
        ORDER BY
            update_time DESC
    </select>

    <select id="selectByKey" resultType="net.lingyue.ly.admin.module.website.config.domain.entity.WebsiteConfigEntity">
        SELECT
            *
        FROM
            t_website_config
        WHERE
            config_key = #{key}
        LIMIT 1
    </select>

</mapper>

package net.lingyue.ly.admin.module.website.content.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentDetailVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentTypeVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentVO;
import net.lingyue.ly.admin.module.website.content.service.ContentService;
import net.lingyue.ly.admin.module.website.content.service.ContentTypeService;
import net.lingyue.ly.base.common.annoation.NoNeedLogin;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 门户网站内容API
 */
@Tag(name = AdminSwaggerTagConst.Website.CONTENT)
@RestController
public class ContentPortalController {

    @Resource
    private ContentService contentService;

    @Resource
    private ContentTypeService contentTypeService;

    /**
     * 获取所有内容类型
     *
     * @return 内容类型列表
     */
    @NoNeedLogin
    @Operation(summary = "门户-获取所有内容类型")
    @GetMapping("/portal/content/types")
    public ResponseDTO<List<ContentTypeVO>> getAllContentTypes() {
        return ResponseDTO.ok(contentTypeService.getAll());
    }

    /**
     * 分页查询内容
     *
     * @param queryForm 查询表单
     * @return 内容列表
     */
    @NoNeedLogin
    @Operation(summary = "门户-分页查询内容")
    @PostMapping("/portal/content/list")
    public ResponseDTO<PageResult<ContentVO>> queryContent(@RequestBody @Valid ContentQueryForm queryForm) {
        // 只查询已发布且未删除的内容
        queryForm.setStatus(1);
        queryForm.setDeletedFlag(false);
        return ResponseDTO.ok(contentService.query(queryForm));
    }

    /**
     * 获取内容详情
     *
     * @param contentId 内容ID
     * @return 内容详情
     */
    @NoNeedLogin
    @Operation(summary = "门户-获取内容详情")
    @GetMapping("/portal/content/detail/{contentId}")
    public ResponseDTO<ContentDetailVO> getContentDetail(@PathVariable Long contentId) {
        ContentDetailVO detailVO = contentService.getDetail(contentId);
        if (detailVO == null) {
            return ResponseDTO.userErrorParam("内容不存在");
        }
        
        // 增加浏览量
        contentService.increasePageViewCount(contentId);
        
        return ResponseDTO.ok(detailVO);
    }

    /**
     * 获取推荐内容列表
     *
     * @param limit 限制数量
     * @return 推荐内容列表
     */
    @NoNeedLogin
    @Operation(summary = "门户-获取推荐内容列表")
    @GetMapping("/portal/content/recommend")
    public ResponseDTO<List<ContentVO>> getRecommendContent(@RequestParam(defaultValue = "5") Integer limit) {
        return ResponseDTO.ok(contentService.getRecommendContent(limit));
    }

    /**
     * 获取置顶内容列表
     *
     * @param limit 限制数量
     * @return 置顶内容列表
     */
    @NoNeedLogin
    @Operation(summary = "门户-获取置顶内容列表")
    @GetMapping("/portal/content/top")
    public ResponseDTO<List<ContentVO>> getTopContent(@RequestParam(defaultValue = "5") Integer limit) {
        return ResponseDTO.ok(contentService.getTopContent(limit));
    }

    /**
     * 根据内容类型获取内容列表
     *
     * @param contentTypeId 内容类型ID
     * @param limit 限制数量
     * @return 内容列表
     */
    @NoNeedLogin
    @Operation(summary = "门户-根据内容类型获取内容列表")
    @GetMapping("/portal/content/byType/{contentTypeId}")
    public ResponseDTO<List<ContentVO>> getContentByType(@PathVariable Long contentTypeId, @RequestParam(defaultValue = "10") Integer limit) {
        return ResponseDTO.ok(contentService.getContentByType(contentTypeId, limit));
    }
}

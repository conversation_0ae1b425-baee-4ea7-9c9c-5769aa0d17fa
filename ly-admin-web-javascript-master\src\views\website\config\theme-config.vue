<!--
  * 网站主题颜色配置
-->
<template>
  <div class="theme-config-container">
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="主色调" name="primaryColor">
          <div class="color-picker-wrapper">
            <a-input 
              v-model:value="formData.primaryColor" 
              placeholder="请输入主色调，如：#d32f2f" 
              @change="validateColor('primaryColor')"
            />
            <input 
              type="color" 
              v-model="formData.primaryColor" 
              class="color-picker"
              @change="validateColor('primaryColor')"
            />
            <div class="color-preview" :style="{ backgroundColor: formData.primaryColor }"></div>
          </div>
          <div class="color-description">网站主要颜色，用于导航栏、按钮等主要元素</div>
        </a-form-item>

        <a-form-item label="主色调深色" name="primaryDarkColor">
          <div class="color-picker-wrapper">
            <a-input 
              v-model:value="formData.primaryDarkColor" 
              placeholder="请输入主色调深色，如：#b71c1c" 
              @change="validateColor('primaryDarkColor')"
            />
            <input 
              type="color" 
              v-model="formData.primaryDarkColor" 
              class="color-picker"
              @change="validateColor('primaryDarkColor')"
            />
            <div class="color-preview" :style="{ backgroundColor: formData.primaryDarkColor }"></div>
          </div>
          <div class="color-description">主色调的深色版本，用于悬停状态和强调元素</div>
        </a-form-item>

        <a-form-item label="链接颜色" name="linkColor">
          <div class="color-picker-wrapper">
            <a-input 
              v-model:value="formData.linkColor" 
              placeholder="请输入链接颜色，如：#d32f2f" 
              @change="validateColor('linkColor')"
            />
            <input 
              type="color" 
              v-model="formData.linkColor" 
              class="color-picker"
              @change="validateColor('linkColor')"
            />
            <div class="color-preview" :style="{ backgroundColor: formData.linkColor }"></div>
          </div>
          <div class="color-description">网站中链接文字的颜色</div>
        </a-form-item>

        <a-form-item label="辅助色" name="secondaryColor">
          <div class="color-picker-wrapper">
            <a-input 
              v-model:value="formData.secondaryColor" 
              placeholder="请输入辅助色，如：#ff5722" 
              @change="validateColor('secondaryColor')"
            />
            <input 
              type="color" 
              v-model="formData.secondaryColor" 
              class="color-picker"
              @change="validateColor('secondaryColor')"
            />
            <div class="color-preview" :style="{ backgroundColor: formData.secondaryColor }"></div>
          </div>
          <div class="color-description">辅助颜色，用于次要按钮和装饰元素</div>
        </a-form-item>

        <a-form-item label="预设主题" name="preset">
          <a-radio-group v-model:value="presetTheme" @change="applyPresetTheme">
            <a-radio value="red">红色主题（当前）</a-radio>
            <a-radio value="blue">蓝色主题</a-radio>
            <a-radio value="green">绿色主题</a-radio>
            <a-radio value="purple">紫色主题</a-radio>
            <a-radio value="orange">橙色主题</a-radio>
          </a-radio-group>
          <div class="preset-preview">
            <div class="preset-colors">
              <div 
                v-for="(color, index) in getPresetColors(presetTheme)" 
                :key="index"
                class="preset-color-block"
                :style="{ backgroundColor: color }"
              ></div>
            </div>
          </div>
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 16, offset: 4 }">
          <a-button type="primary" @click="handleSubmit">保存</a-button>
          <a-button @click="handleReset" style="margin-left: 8px;">重置</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { websiteConfigApi } from '/@/api/website/website-config-api';

const formRef = ref();
const loading = ref(false);
const presetTheme = ref('red');

const formData = ref({
  primaryColor: '#d32f2f',
  primaryDarkColor: '#b71c1c',
  linkColor: '#d32f2f',
  secondaryColor: '#ff5722'
});

const rules = {
  primaryColor: [
    { required: true, message: '请输入主色调', trigger: 'blur' },
    { pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, message: '请输入有效的颜色值，如：#d32f2f', trigger: 'blur' }
  ],
  primaryDarkColor: [
    { required: true, message: '请输入主色调深色', trigger: 'blur' },
    { pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, message: '请输入有效的颜色值，如：#b71c1c', trigger: 'blur' }
  ],
  linkColor: [
    { required: true, message: '请输入链接颜色', trigger: 'blur' },
    { pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, message: '请输入有效的颜色值，如：#d32f2f', trigger: 'blur' }
  ],
  secondaryColor: [
    { pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, message: '请输入有效的颜色值，如：#ff5722', trigger: 'blur' }
  ]
};

// 预设主题配置
const themePresets = {
  red: {
    primaryColor: '#d32f2f',
    primaryDarkColor: '#b71c1c',
    linkColor: '#d32f2f',
    secondaryColor: '#ff5722'
  },
  blue: {
    primaryColor: '#1976d2',
    primaryDarkColor: '#1565c0',
    linkColor: '#1976d2',
    secondaryColor: '#2196f3'
  },
  green: {
    primaryColor: '#388e3c',
    primaryDarkColor: '#2e7d32',
    linkColor: '#388e3c',
    secondaryColor: '#4caf50'
  },
  purple: {
    primaryColor: '#7b1fa2',
    primaryDarkColor: '#6a1b9a',
    linkColor: '#7b1fa2',
    secondaryColor: '#9c27b0'
  },
  orange: {
    primaryColor: '#f57c00',
    primaryDarkColor: '#ef6c00',
    linkColor: '#f57c00',
    secondaryColor: '#ff9800'
  }
};

// 获取预设主题颜色
const getPresetColors = (theme) => {
  const preset = themePresets[theme] || themePresets.red;
  return [preset.primaryColor, preset.primaryDarkColor, preset.linkColor, preset.secondaryColor];
};

// 应用预设主题
const applyPresetTheme = () => {
  const preset = themePresets[presetTheme.value];
  if (preset) {
    formData.value = { ...preset };
  }
};

// 验证颜色值
const validateColor = (field) => {
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  if (!colorRegex.test(formData.value[field])) {
    // 如果不是有效的颜色值，尝试添加#号
    if (!formData.value[field].startsWith('#')) {
      formData.value[field] = '#' + formData.value[field];
    }
  }
};

// 获取主题颜色配置
const fetchConfig = async () => {
  loading.value = true;
  try {
    const res = await websiteConfigApi.getWebsiteThemeConfig();
    if (res && res.data) {
      formData.value = {
        primaryColor: res.data.primaryColor || '#d32f2f',
        primaryDarkColor: res.data.primaryDarkColor || '#b71c1c',
        linkColor: res.data.linkColor || '#d32f2f',
        secondaryColor: res.data.secondaryColor || '#ff5722'
      };
      // 识别当前使用的预设主题
      identifyPresetTheme();
    }
  } catch (error) {
    console.error('获取主题颜色配置失败:', error);
    message.error('获取主题颜色配置失败');
  } finally {
    loading.value = false;
  }
};

// 识别当前预设主题
const identifyPresetTheme = () => {
  for (const [key, preset] of Object.entries(themePresets)) {
    if (preset.primaryColor === formData.value.primaryColor &&
        preset.primaryDarkColor === formData.value.primaryDarkColor &&
        preset.linkColor === formData.value.linkColor &&
        preset.secondaryColor === formData.value.secondaryColor) {
      presetTheme.value = key;
      return;
    }
  }
  presetTheme.value = 'custom';
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    await websiteConfigApi.updateWebsiteThemeConfig(formData.value);
    message.success('主题颜色配置保存成功');
    
  } catch (error) {
    console.error('保存主题颜色配置失败:', error);
    message.error('保存主题颜色配置失败');
  } finally {
    loading.value = false;
  }
};

// 重置表单
const handleReset = () => {
  formData.value = {
    primaryColor: '#d32f2f',
    primaryDarkColor: '#b71c1c',
    linkColor: '#d32f2f',
    secondaryColor: '#ff5722'
  };
  presetTheme.value = 'red';
};

// 暴露方法给父组件调用
defineExpose({
  fetchConfig
});

onMounted(() => {
  fetchConfig();
});
</script>

<style lang="less" scoped>
.theme-config-container {
  padding: 20px;
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .ant-input {
    flex: 1;
  }
  
  .color-picker {
    width: 40px;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    background: none;
    
    &::-webkit-color-swatch {
      border: none;
      border-radius: 4px;
    }
  }
  
  .color-preview {
    width: 40px;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    flex-shrink: 0;
  }
}

.color-description {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.preset-preview {
  margin-top: 8px;
  
  .preset-colors {
    display: flex;
    gap: 4px;
    
    .preset-color-block {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
    }
  }
}
</style> 
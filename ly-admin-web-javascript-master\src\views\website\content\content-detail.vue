<!--
  * 内容详情
-->
<template>
  <a-card :bordered="false" :loading="loading">
    <template #title>
      <a-space>
        <a-button @click="goBack">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
        <span>内容详情</span>
      </a-space>
    </template>

    <div class="content-detail">
      <h1 class="content-title">{{ contentDetail.title }}</h1>
      <div class="content-meta">
        <a-space>
          <span>分类：{{ contentDetail.contentTypeName }}</span>
          <a-divider type="vertical" />
          <span>作者：{{ contentDetail.author }}</span>
          <a-divider type="vertical" />
          <span>来源：{{ contentDetail.source }}</span>
          <a-divider type="vertical" />
          <span>发布时间：{{ contentDetail.publishTime }}</span>
          <a-divider type="vertical" />
          <span>浏览量：{{ contentDetail.pageViewCount }}</span>
        </a-space>
      </div>

      <div class="content-tags">
        <a-space>
          <a-tag v-if="contentDetail.topFlag" color="success">置顶</a-tag>
          <a-tag v-if="contentDetail.recommendFlag" color="success">推荐</a-tag>
          <a-tag v-if="contentDetail.status === CONTENT_STATUS_ENUM.DRAFT.value" color="warning">草稿</a-tag>
          <a-tag v-if="contentDetail.status === CONTENT_STATUS_ENUM.PUBLISHED.value" color="success">已发布</a-tag>
          <a-tag v-if="contentDetail.status === CONTENT_STATUS_ENUM.OFFLINE.value" color="error">已下线</a-tag>
        </a-space>
      </div>

      <div class="content-summary" v-if="contentDetail.summary">
        <div class="summary-title">摘要：</div>
        <div class="summary-content">{{ contentDetail.summary }}</div>
      </div>

      <div class="content-html" v-html="contentDetail.contentHtml"></div>

      <div class="content-attachments" v-if="contentDetail.attachment && contentDetail.attachment.length > 0">
        <div class="attachments-title">附件：</div>
        <div class="attachments-list">
          <div v-for="(item, index) in contentDetail.attachment" :key="index" class="attachment-item">
            <FilePreview :fileId="item" />
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { contentApi } from '/@/api/website/content-api';
  import { CONTENT_STATUS_ENUM } from '/@/constants/website/content-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FilePreview from '/@/components/support/file-preview/index.vue';

  const router = useRouter();
  const route = useRoute();
  const contentId = ref(route.query.contentId);
  const loading = ref(false);
  const contentDetail = ref({});

  onMounted(() => {
    getContentDetail();
  });

  // 获取内容详情
  async function getContentDetail() {
    if (!contentId.value) {
      return;
    }
    try {
      loading.value = true;
      const result = await contentApi.getContentDetail(contentId.value);
      contentDetail.value = result.data || {};
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      loading.value = false;
    }
  }

  // 返回
  function goBack() {
    router.back();
  }
</script>

<style lang="less" scoped>
  .content-detail {
    padding: 20px;

    .content-title {
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }

    .content-meta {
      text-align: center;
      color: #666;
      margin-bottom: 20px;
    }

    .content-tags {
      text-align: center;
      margin-bottom: 20px;
    }

    .content-summary {
      background-color: #f5f5f5;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 4px;

      .summary-title {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .summary-content {
        color: #666;
      }
    }

    .content-html {
      margin-bottom: 30px;
      line-height: 1.8;
    }

    .content-attachments {
      border-top: 1px solid #eee;
      padding-top: 20px;

      .attachments-title {
        font-weight: bold;
        margin-bottom: 10px;
      }

      .attachments-list {
        .attachment-item {
          margin-bottom: 5px;
        }
      }
    }
  }
</style>

<style>
  .content-html img {
    max-width: 100%;
    height: auto;
  }
</style>

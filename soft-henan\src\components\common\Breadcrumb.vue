<template>
  <div class="breadcrumb-container" v-if="breadcrumbItems.length > 0 && !isHomePage">
    <a-breadcrumb separator=">">
      <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
        <router-link v-if="item.path && index < breadcrumbItems.length - 1" :to="item.path">
          {{ item.title }}
        </router-link>
        <span v-else>{{ item.title }}</span>
      </a-breadcrumb-item>
    </a-breadcrumb>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '../../store/modules/appStore'

const route = useRoute()
const appStore = useAppStore()

// 判断是否为首页
const isHomePage = computed(() => {
  return route.path === '/' || route.path === '/home'
})

// 根据路径生成标题
const getTitleFromPath = (path) => {
  const titleMap = {
    'news': '新闻资讯',
    'about': '学会',
    'activity': '活动',
    'policy': '政策',
    'service': '服务',
    'join': '入会',
    'representative': '代表处',
    'branch': '分会',
    'regulation': '管理规定',
    'certificate': '证书查询',
    'charity': '公益活动',
    'academic': '学术活动',
    'training': '培训活动',
    'conference': '会议活动'
  }
  
  return titleMap[path] || path
}

// 根据当前路由生成面包屑
const breadcrumbItems = computed(() => {
  const items = []
  
  // 如果是首页，不显示面包屑
  if (isHomePage.value) {
    return items
  }
  
  // 始终添加首页
  items.push({
    title: '首页',
    path: '/'
  })
  
  // 根据当前路由路径生成面包屑
  const pathSegments = route.path.split('/').filter(segment => segment)
  
  if (pathSegments.length === 0) {
    return items
  }
  
  // 获取菜单数据
  const menuList = appStore.menuList
  const topMenuList = appStore.getTopMenuList
  
  // 处理第一级菜单
  const firstSegment = pathSegments[0]
  const firstLevelMenu = topMenuList.find(menu => {
    if (menu.path) {
      const menuPath = menu.path.replace(/^\//, '')
      return menuPath === firstSegment
    }
    return false
  })
  
  if (firstLevelMenu) {
    items.push({
      title: firstLevelMenu.menuName,
      path: firstLevelMenu.path
    })
    
    // 处理第二级菜单（如果有）
    if (pathSegments.length > 1) {
      const secondSegment = pathSegments[1]
      
      // 查找子菜单
      const childrenMenus = appStore.getChildrenMenus(firstLevelMenu.menuId)
      const secondLevelMenu = childrenMenus.find(subMenu => {
        if (subMenu.path) {
          const subMenuPath = subMenu.path.split('/').pop()
          return subMenuPath === secondSegment
        }
        return false
      })
      
      if (secondLevelMenu) {
        items.push({
          title: secondLevelMenu.menuName,
          path: secondLevelMenu.path
        })
      } else {
        // 如果没有找到对应的子菜单，尝试使用路径映射
        const mappedTitle = getTitleFromPath(secondSegment)
        if (mappedTitle !== secondSegment) {
          items.push({
            title: mappedTitle,
            path: null
          })
        }
      }
    }
    
    // 处理详情页面
    if (route.path.includes('/detail/')) {
      const detailId = route.params.id
      if (detailId) {
        // 尝试从路由元数据或页面标题获取详情页标题
        const detailTitle = route.meta?.title || '详情'
        items.push({
          title: detailTitle,
          path: null
        })
      }
    }
  } else {
    // 如果没有找到对应的菜单，尝试通过路径匹配子菜单
    // 遍历所有顶级菜单，查找包含当前路径的子菜单
    for (const topMenu of topMenuList) {
      const childrenMenus = appStore.getChildrenMenus(topMenu.menuId)
      const matchedSubMenu = childrenMenus.find(subMenu => {
        if (subMenu.path) {
          const subMenuPath = subMenu.path.replace(/^\//, '')
          return subMenuPath === firstSegment
        }
        return false
      })
      
      if (matchedSubMenu) {
        // 找到了匹配的子菜单，添加父菜单和子菜单
        items.push({
          title: topMenu.menuName,
          path: topMenu.path
        })
        items.push({
          title: matchedSubMenu.menuName,
          path: matchedSubMenu.path
        })
        break
      }
    }
    
    // 如果还是没有找到，使用路径作为标题
    if (items.length === 1) { // 只有首页
      pathSegments.forEach((segment, index) => {
        const path = '/' + pathSegments.slice(0, index + 1).join('/')
        const mappedTitle = getTitleFromPath(segment)
        items.push({
          title: mappedTitle,
          path: index < pathSegments.length - 1 ? path : null
        })
      })
    }
  }
  
  return items
})
</script>

<style scoped>
.breadcrumb-container {
  padding: 20px 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  width: 100%;
  position: relative;
  z-index: 10;
  margin-top: 100px; /* 大幅增加与菜单的距离 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 添加轻微阴影增加分区效果 */
}

.breadcrumb-container :deep(.ant-breadcrumb) {
  font-size: 14px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.breadcrumb-container :deep(.ant-breadcrumb a) {
  color: #666;
  text-decoration: none;
}

.breadcrumb-container :deep(.ant-breadcrumb a:hover) {
  color: var(--primary-color, #d32f2f);
}

.breadcrumb-container :deep(.ant-breadcrumb .ant-breadcrumb-item:last-child) {
  color: #333;
  font-weight: 500;
}

.breadcrumb-container :deep(.ant-breadcrumb .ant-breadcrumb-separator) {
  margin: 0 8px;
  color: #999;
}
</style>

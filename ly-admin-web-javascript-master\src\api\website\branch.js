/*
 * 分会管理 API
 */
import { postRequest, getRequest } from '/@/lib/axios';

// 获取所有分会
export function getAllBranch() {
  return getRequest('/website/branch/getAll');
}

// 分页查询分会
export function queryBranch(queryForm) {
  return postRequest('/website/branch/page', queryForm);
}

// 添加分会
export function addBranch(param) {
  return postRequest('/website/branch/add', param);
}

// 更新分会
export function updateBranch(param) {
  return postRequest('/website/branch/update', param);
}

// 删除分会
export function deleteBranch(branchId) {
  return getRequest(`/website/branch/delete/${branchId}`);
}

// 获取分会详情
export function getBranchDetail(branchId) {
  return getRequest(`/website/branch/get/${branchId}`);
}

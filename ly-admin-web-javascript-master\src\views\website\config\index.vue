<!--
  * 网站配置管理
-->
<template>
  <div class="website-config-container">
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane :key="WEBSITE_CONFIG_TAB_ENUM.BASIC.value" :tab="WEBSITE_CONFIG_TAB_ENUM.BASIC.desc">
          <BasicConfig ref="basicConfigRef" />
        </a-tab-pane>
        <a-tab-pane :key="WEBSITE_CONFIG_TAB_ENUM.CAROUSEL.value" :tab="WEBSITE_CONFIG_TAB_ENUM.CAROUSEL.desc">
          <CarouselConfig ref="carouselConfigRef" />
        </a-tab-pane>
        <a-tab-pane :key="WEBSITE_CONFIG_TAB_ENUM.FOOTER.value" :tab="WEBSITE_CONFIG_TAB_ENUM.FOOTER.desc">
          <FooterConfig ref="footerConfigRef" />
        </a-tab-pane>
        <a-tab-pane :key="WEBSITE_CONFIG_TAB_ENUM.THEME.value" :tab="WEBSITE_CONFIG_TAB_ENUM.THEME.desc">
          <ThemeConfig ref="themeConfigRef" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BasicConfig from './basic-config.vue';
import CarouselConfig from './carousel-config.vue';
import FooterConfig from './footer-config.vue';
import ThemeConfig from './theme-config.vue';

// 网站配置选项卡枚举
const WEBSITE_CONFIG_TAB_ENUM = {
  BASIC: { value: 'basic', desc: '基本信息' },
  CAROUSEL: { value: 'carousel', desc: '轮播图' },
  FOOTER: { value: 'footer', desc: '底部信息' },
  THEME: { value: 'theme', desc: '主题颜色' }
};

const activeTab = ref(WEBSITE_CONFIG_TAB_ENUM.BASIC.value);
const basicConfigRef = ref();
const carouselConfigRef = ref();
const footerConfigRef = ref();
const themeConfigRef = ref();

// 切换选项卡
const handleTabChange = (key) => {
  if (key === WEBSITE_CONFIG_TAB_ENUM.BASIC.value) {
    basicConfigRef.value?.fetchConfig();
  } else if (key === WEBSITE_CONFIG_TAB_ENUM.CAROUSEL.value) {
    carouselConfigRef.value?.fetchConfig();
  } else if (key === WEBSITE_CONFIG_TAB_ENUM.FOOTER.value) {
    footerConfigRef.value?.fetchConfig();
  } else if (key === WEBSITE_CONFIG_TAB_ENUM.THEME.value) {
    themeConfigRef.value?.fetchConfig();
  }
};
</script>

<style lang="less" scoped>
.website-config-container {
  padding: 20px;
}
</style>

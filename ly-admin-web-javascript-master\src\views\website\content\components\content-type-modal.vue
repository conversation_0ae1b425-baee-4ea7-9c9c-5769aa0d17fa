<!--
  * 内容类型管理
-->
<template>
  <a-modal
    :title="'内容类型管理'"
    :open="visibleFlag"
    :width="800"
    :footer="null"
    @cancel="onClose"
    :destroyOnClose="true"
  >
    <a-card size="small" :bordered="false">
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button type="primary" @click="showAddModal()" v-privilege="'website:content:type:add'">
            <template #icon>
              <PlusOutlined />
            </template>
            新建
          </a-button>
        </div>
      </a-row>

      <a-table
        rowKey="contentTypeId"
        :columns="tableColumns"
        :dataSource="tableData"
        :pagination="false"
        :loading="tableLoading"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'enableFlag'">
            <a-tag v-show="text" color="success">启用</a-tag>
            <a-tag v-show="!text" color="error">禁用</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button type="link" @click="showEditModal(record)" v-privilege="'website:content:type:update'">编辑</a-button>
              <a-button type="link" @click="updateStatus(record.contentTypeId, !record.enableFlag)" v-privilege="'website:content:type:update'">
                {{ record.enableFlag ? '禁用' : '启用' }}
              </a-button>
              <a-button type="link" @click="onDelete(record.contentTypeId)" v-privilege="'website:content:type:delete'" danger>删除</a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑内容类型弹窗 -->
    <a-modal
      :title="editingContentType.contentTypeId ? '编辑内容类型' : '添加内容类型'"
      :open="typeFormVisible"
      @ok="submitTypeForm"
      @cancel="typeFormVisible = false"
      :confirmLoading="typeFormLoading"
    >
      <a-form ref="typeFormRef" :model="editingContentType" :rules="typeFormRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="类型名称" name="contentTypeName">
          <a-input v-model:value="editingContentType.contentTypeName" placeholder="请输入类型名称" />
        </a-form-item>
        <a-form-item label="类型编码" name="contentTypeCode" v-if="!editingContentType.contentTypeId">
          <a-input v-model:value="editingContentType.contentTypeCode" placeholder="请输入类型编码" />
        </a-form-item>
        <a-form-item label="类型描述" name="contentTypeDesc">
          <a-textarea v-model:value="editingContentType.contentTypeDesc" placeholder="请输入类型描述" :rows="3" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="editingContentType.sort" :min="0" :max="9999" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { contentApi } from '/@/api/website/content-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emits = defineEmits(['reloadList']);

  // ------------------ 显示，关闭 ------------------
  // 显示
  const visibleFlag = ref(false);
  function showModal() {
    visibleFlag.value = true;
    queryContentTypeList();
  }

  // 关闭
  function onClose() {
    visibleFlag.value = false;
  }

  // ------------------ 表格 ------------------
  const tableColumns = ref([
    {
      title: '类型名称',
      dataIndex: 'contentTypeName',
      width: 150,
    },
    {
      title: '类型编码',
      dataIndex: 'contentTypeCode',
      width: 120,
    },
    {
      title: '类型描述',
      dataIndex: 'contentTypeDesc',
      width: 200,
    },
    {
      title: '排序',
      dataIndex: 'sort',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'enableFlag',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
    },
  ]);

  const tableData = ref([]);
  const tableLoading = ref(false);

  // 查询内容类型列表
  async function queryContentTypeList() {
    try {
      tableLoading.value = true;
      const result = await contentApi.getAllContentTypes();
      tableData.value = result.data;
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      tableLoading.value = false;
    }
  }

  // ------------------ 添加/编辑内容类型 ------------------
  const typeFormVisible = ref(false);
  const typeFormLoading = ref(false);
  const typeFormRef = ref();
  const defaultContentType = {
    contentTypeId: undefined,
    contentTypeName: '',
    contentTypeCode: '',
    contentTypeDesc: '',
    sort: 0,
  };
  const editingContentType = reactive({ ...defaultContentType });

  const typeFormRules = {
    contentTypeName: [{ required: true, message: '请输入类型名称' }],
    contentTypeCode: [{ required: true, message: '请输入类型编码' }],
    sort: [{ required: true, message: '请输入排序' }],
  };

  // 显示添加弹窗
  function showAddModal() {
    Object.assign(editingContentType, defaultContentType);
    typeFormVisible.value = true;
  }

  // 显示编辑弹窗
  function showEditModal(record) {
    Object.assign(editingContentType, record);
    typeFormVisible.value = true;
  }

  // 提交表单
  async function submitTypeForm() {
    try {
      await typeFormRef.value.validateFields();
      typeFormLoading.value = true;

      let res;
      if (editingContentType.contentTypeId) {
        // 编辑
        res = await contentApi.updateContentType(
          editingContentType.contentTypeId,
          editingContentType.contentTypeName,
          editingContentType.contentTypeDesc,
          editingContentType.sort
        );
      } else {
        // 添加
        res = await contentApi.addContentType(
          editingContentType.contentTypeName,
          editingContentType.contentTypeCode,
          editingContentType.contentTypeDesc,
          editingContentType.sort
        );
      }

      if (res && (res.code === 0 || res.code === 1)) {
        message.success(editingContentType.contentTypeId ? '更新成功' : '添加成功');
        typeFormVisible.value = false;
        queryContentTypeList();
        emits('reloadList');
      } else {
        message.error(res?.msg || '操作失败');
      }
    } catch (err) {
      if (err.errorFields) {
        message.error('请填写完整表单信息');
      } else {
        smartSentry.captureError(err);
      }
    } finally {
      typeFormLoading.value = false;
    }
  }

  // ------------------ 更新状态 ------------------
  async function updateStatus(contentTypeId, enableFlag) {
    try {
      SmartLoading.show();
      const res = await contentApi.updateContentTypeStatus(contentTypeId, enableFlag);
      if (res && (res.code === 0 || res.code === 1)) {
        message.success(enableFlag ? '启用成功' : '禁用成功');
        queryContentTypeList();
        emits('reloadList');
      } else {
        message.error(res?.msg || '操作失败');
      }
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // ------------------ 删除 ------------------
  function onDelete(contentTypeId) {
    Modal.confirm({
      title: '提示',
      content: '确认删除此内容类型吗?',
      onOk() {
        deleteContentType(contentTypeId);
      },
    });
  }

  async function deleteContentType(contentTypeId) {
    try {
      SmartLoading.show();
      const res = await contentApi.deleteContentType(contentTypeId);
      if (res && (res.code === 0 || res.code === 1)) {
        message.success('删除成功');
        queryContentTypeList();
        emits('reloadList');
      } else {
        message.error(res?.msg || '删除失败');
      }
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // ----------------------- 以下是暴露的方法内容 ------------------------
  defineExpose({
    showModal,
  });
</script>

<style lang="less" scoped></style>

import { defineStore } from 'pinia'
import { menuApi } from '../../api/menu'
import configApi from '../../api/config'
import ThemeUtil from '../../utils/themeUtil'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 菜单数据
    menuList: [],
    topMenuList: [],
    menuLoaded: false,
    menuLoading: false, // 添加加载状态

    // 网站基本配置
    websiteConfig: {
      socialCreditCode: '',
      websiteLogo: '',
      organizationName: '河南省软组织病研究会',
      organizationEnglishName: 'Henan Association for Soft Tissue Disease Research',
      icpNumber: '',
      publicSecurityNumber: '',
      copyrightInfo: '',
      legalAdvisorInfo: ''
    },
    configLoaded: false,

    // 网站底部配置
    footerConfig: {
      address: '',
      phone: '',
      email: '',
      wechat: '',
      qrCode: '',
      links: []
    },
    footerConfigLoaded: false,

    // 网站轮播图配置
    carouselConfig: {
      items: []
    },
    carouselConfigLoaded: false,

    // 网站主题颜色配置
    themeConfig: {
      primaryColor: '#d32f2f',      // 主色调
      primaryDarkColor: '#b71c1c',  // 主色调深色
      linkColor: '#d32f2f',         // 链接颜色
      secondaryColor: '#ff5722'     // 辅助色
    },
    themeConfigLoaded: false
  }),

  getters: {
    // 获取顶级菜单
    getTopMenuList: (state) => {
      return state.topMenuList
    },

    // 获取指定父菜单ID下的子菜单
    getChildrenMenus: (state) => (parentId) => {
      // 确保parentId类型一致
      const parentIdStr = String(parentId)
      return state.menuList.filter(menu => {
        const menuParentId = menu.parentId === 0 ? 0 : String(menu.parentId)
        return menuParentId === parentIdStr
      }).sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
    },

    // 获取网站基本配置
    getWebsiteConfig: (state) => {
      return state.websiteConfig
    },

    // 获取网站底部配置
    getFooterConfig: (state) => {
      return state.footerConfig
    },

    // 获取网站轮播图配置
    getCarouselConfig: (state) => {
      return state.carouselConfig
    },

    // 获取网站主题颜色配置
    getThemeConfig: (state) => {
      return state.themeConfig
    }
  },

  actions: {
    // 加载菜单数据
    async loadMenuList() {
      // 如果正在加载，避免重复请求
      if (this.menuLoading) {
        console.log('菜单正在加载中，跳过重复请求')
        return this.menuList
      }

      // 如果已经加载过，则不再重复加载
      if (this.menuLoaded) {
        console.log('菜单已加载过，使用缓存数据')
        return this.menuList
      }

      this.menuLoading = true
      console.log('开始加载菜单数据...')

      try {
        // 获取菜单数据
        console.log('调用菜单API: /website/menu/front')
        const menuRes = await menuApi.getMenuList()
        console.log('菜单API原始响应:', menuRes)

        // 尝试从不同的响应格式中提取菜单数据
        let menuData = null

        if (menuRes && Array.isArray(menuRes)) {
          // 如果响应本身就是数组
          menuData = menuRes
        } else if (menuRes && menuRes.data && Array.isArray(menuRes.data)) {
          // 标准格式：res.data 是数组
          menuData = menuRes.data
        } else if (menuRes && menuRes.data && typeof menuRes.data === 'object' && !Array.isArray(menuRes.data)) {
          // 如果 res.data 是对象，尝试找到其中的数组属性
          const arrayProps = Object.keys(menuRes.data).filter(key => Array.isArray(menuRes.data[key]))
          if (arrayProps.length > 0) {
            menuData = menuRes.data[arrayProps[0]]
          }
        }

        if (menuData && Array.isArray(menuData) && menuData.length > 0) {
          console.log('提取的菜单数据:', menuData)

          // 处理菜单数据 - 简化逻辑
          const processedMenuData = menuData
            .filter(menu =>
              menu &&
              typeof menu === 'object' &&
              menu.menuName &&
              menu.visibleFlag !== false && // 只过滤掉明确设置为false的
              menu.disabledFlag !== true && // 只过滤掉明确设置为true的
              menu.deletedFlag !== true // 过滤掉已删除的
            )
            .map(menu => ({
              ...menu,
              // 统一字段名称，确保排序字段正确
              orderNum: menu.sort || menu.orderNum || 0,
              // 确保菜单类型正确
              menuType: menu.menuType || 2,
              // 确保布尔值正确
              visibleFlag: menu.visibleFlag !== false,
              disabledFlag: menu.disabledFlag === true,
              deletedFlag: menu.deletedFlag === true,
              // 统一ID类型为字符串，确保父子关系匹配
              menuId: String(menu.menuId),
              parentId: menu.parentId === 0 ? 0 : String(menu.parentId)
            }))

          if (processedMenuData.length > 0) {
            // 验证菜单数据的完整性
            const validationResult = this.validateMenuData(processedMenuData)
            if (!validationResult.isValid) {
              console.warn('菜单数据验证失败:', validationResult.errors)
            }

            // 确保菜单数据中包含首页菜单项
            const hasHomeMenu = processedMenuData.some(menu =>
              (menu.path === '/' || menu.menuName === '首页') && menu.parentId === 0
            )

            // 如果没有首页菜单，添加一个
            if (!hasHomeMenu) {
              processedMenuData.unshift({
                menuId: 'home',
                menuName: '首页',
                path: '/',
                parentId: 0,
                menuType: 2,
                visibleFlag: true,
                disabledFlag: false,
                deletedFlag: false,
                orderNum: -1 // 确保排在最前面
              })
            }

            this.menuList = processedMenuData
            // 获取顶级菜单并按排序号排序
            this.topMenuList = processedMenuData
              .filter(menu => menu.parentId === 0)
              .sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))

            this.menuLoaded = true
            console.log('设置菜单数据:', this.menuList)
            console.log('设置顶级菜单:', this.topMenuList)
          } else {
            console.warn('处理后的菜单数据为空，使用默认菜单')
            this.setDefaultMenuData()
          }
        } else {
          console.warn('API返回的菜单数据为空或格式不正确，使用默认菜单')
          this.setDefaultMenuData()
        }

        return this.menuList
      } catch (error) {
        console.error('获取菜单数据失败:', error)
        console.warn('使用默认菜单数据')
        this.setDefaultMenuData()
        return this.menuList
      } finally {
        this.menuLoading = false
      }
    },

    // 验证菜单数据
    validateMenuData(menuData) {
      const errors = []
      
      // 检查是否有重复的菜单ID
      const menuIds = menuData.map(menu => menu.menuId)
      const duplicateIds = menuIds.filter((id, index) => menuIds.indexOf(id) !== index)
      if (duplicateIds.length > 0) {
        errors.push(`发现重复的菜单ID: ${duplicateIds.join(', ')}`)
      }

      // 检查子菜单的父菜单是否存在
      const parentIds = new Set(menuData.map(menu => menu.parentId))
      const allMenuIds = new Set(menuData.map(menu => menu.menuId))
      
      for (const parentId of parentIds) {
        if (parentId !== 0 && !allMenuIds.has(parentId)) {
          errors.push(`子菜单的父菜单ID ${parentId} 不存在`)
        }
      }

      // 检查是否有循环引用
      const visited = new Set()
      const recursionStack = new Set()
      
      const hasCycle = (menuId) => {
        if (recursionStack.has(menuId)) {
          return true
        }
        if (visited.has(menuId)) {
          return false
        }
        
        visited.add(menuId)
        recursionStack.add(menuId)
        
        const children = menuData.filter(menu => menu.parentId === menuId)
        for (const child of children) {
          if (hasCycle(child.menuId)) {
            return true
          }
        }
        
        recursionStack.delete(menuId)
        return false
      }
      
      for (const menu of menuData) {
        if (!visited.has(menu.menuId) && hasCycle(menu.menuId)) {
          errors.push(`发现菜单循环引用: ${menu.menuId}`)
        }
      }

      return {
        isValid: errors.length === 0,
        errors
      }
    },

    // 设置默认菜单数据
    setDefaultMenuData() {
      const defaultMenuData = [
        {
          menuId: 'home',
          menuName: '首页',
          path: '/',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 1
        },
        {
          menuId: 'about',
          menuName: '学会',
          path: '/about',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 2
        },
        {
          menuId: 'party',
          menuName: '党建',
          path: '/party',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 3
        },
        {
          menuId: 'activity',
          menuName: '活动',
          path: '/activity',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 4
        },
        {
          menuId: 'news',
          menuName: '资讯',
          path: '/news',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 5
        },
        {
          menuId: 'service',
          menuName: '服务',
          path: '/service',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 6
        },
        {
          menuId: 'policy',
          menuName: '政策',
          path: '/policy',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 7
        },
        {
          menuId: 'join',
          menuName: '入会',
          path: '/join',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 8
        },
        {
          menuId: 'branch',
          menuName: '分支机构',
          path: null,
          parentId: 0,
          menuType: 1, // 目录类型
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 9
        },
        {
          menuId: 'branch-office',
          menuName: '代表处',
          path: '/representative',
          parentId: 'branch', // 使用字符串ID，与父菜单保持一致
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 1
        },
        {
          menuId: 'branch-sub',
          menuName: '分会',
          path: '/branch',
          parentId: 'branch', // 使用字符串ID，与父菜单保持一致
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 2
        },
        {
          menuId: 'branch-regulation',
          menuName: '管理规定',
          path: '/regulation',
          parentId: 'branch', // 使用字符串ID，与父菜单保持一致
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 3
        },
        {
          menuId: 'cert',
          menuName: '证书查询',
          path: '/cert',
          parentId: 0,
          menuType: 2,
          visibleFlag: true,
          disabledFlag: false,
          deletedFlag: false,
          orderNum: 10
        }
      ]

      this.menuList = defaultMenuData
      this.topMenuList = defaultMenuData
        .filter(menu => menu.parentId === 0)
        .sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
      this.menuLoaded = true
      
      console.log('使用默认菜单数据:', this.menuList)
    },

    // 加载网站基本配置
    async loadWebsiteConfig() {
      // 如果已经加载过，则不再重复加载
      if (this.configLoaded) {
        return this.websiteConfig
      }

      try {
        // 获取网站基本信息配置
        const configRes = await configApi.getWebsiteBasicConfig()
        if (configRes && configRes.data) {
          this.websiteConfig = configRes.data
          this.configLoaded = true
          console.log('网站基本信息配置:', this.websiteConfig)
        }

        return this.websiteConfig
      } catch (error) {
        console.error('获取网站基本配置失败:', error)
        return this.websiteConfig
      }
    },

    // 加载网站底部配置
    async loadFooterConfig() {
      // 如果已经加载过，则不再重复加载
      if (this.footerConfigLoaded) {
        return this.footerConfig
      }

      try {
        // 获取网站底部信息配置
        const footerRes = await configApi.getWebsiteFooterConfig()
        if (footerRes && footerRes.data) {
          this.footerConfig = footerRes.data
          this.footerConfigLoaded = true
          console.log('网站底部信息配置:', this.footerConfig)
        }

        return this.footerConfig
      } catch (error) {
        console.error('获取网站底部配置失败:', error)
        return this.footerConfig
      }
    },

    // 加载网站轮播图配置
    async loadCarouselConfig() {
      // 如果已经加载过，则不再重复加载
      if (this.carouselConfigLoaded) {
        return this.carouselConfig
      }

      try {
        // 获取网站轮播图配置
        const carouselRes = await configApi.getWebsiteCarouselConfig()
        if (carouselRes && carouselRes.data) {
          this.carouselConfig = carouselRes.data
          this.carouselConfigLoaded = true
          console.log('网站轮播图配置:', this.carouselConfig)
        }

        return this.carouselConfig
      } catch (error) {
        console.error('获取网站轮播图配置失败:', error)
        return this.carouselConfig
      }
    },

    // 加载网站主题颜色配置
    async loadThemeConfig() {
      // 如果已经加载过，则不再重复加载
      if (this.themeConfigLoaded) {
        return this.themeConfig
      }

      try {
        // 获取网站主题颜色配置
        const themeRes = await configApi.getWebsiteThemeConfig()
        if (themeRes && themeRes.data) {
          this.themeConfig = {
            primaryColor: themeRes.data.primaryColor || '#d32f2f',
            primaryDarkColor: themeRes.data.primaryDarkColor || '#b71c1c',
            linkColor: themeRes.data.linkColor || '#d32f2f',
            secondaryColor: themeRes.data.secondaryColor || '#ff5722'
          }
          this.themeConfigLoaded = true
          console.log('网站主题颜色配置:', this.themeConfig)
          
          // 应用主题颜色到CSS变量
          this.applyThemeColors()
        }

        return this.themeConfig
      } catch (error) {
        console.error('获取网站主题颜色配置失败:', error)
        return this.themeConfig
      }
    },

    // 应用主题颜色到CSS变量
    applyThemeColors() {
      // 使用主题工具应用配置
      ThemeUtil.applyThemeConfig(this.themeConfig)
      ThemeUtil.createThemeStylesheet(this.themeConfig)
      console.log('已应用主题颜色:', this.themeConfig)
    },

    // 初始化所有配置
    async initAllConfig() {
      await Promise.all([
        this.loadMenuList(),
        this.loadWebsiteConfig(),
        this.loadFooterConfig(),
        this.loadCarouselConfig(),
        this.loadThemeConfig()
      ])
    }
  }
})

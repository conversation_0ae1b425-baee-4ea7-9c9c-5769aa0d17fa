import './style.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'
import App from './App.vue'
import Antd from 'ant-design-vue'

// 获取环境变量中的base路径
const basePath = import.meta.env.VITE_BASE_PATH || '/'

// 创建路由器实例
const router = createRouter({
  history: createWebHistory(basePath), // 使用环境变量中的base路径
  routes,
  // 添加全局滚动行为
  scrollBehavior(to, from, savedPosition) {
    // 简化滚动行为，避免复杂的异步操作导致状态问题
    try {
      if (savedPosition) {
        return savedPosition
      }
      return { top: 0 }
    } catch (error) {
      console.error('ScrollBehavior error:', error)
      return { top: 0 }
    }
  }
})

// 添加路由守卫来处理导航错误
router.onError((error) => {
  console.error('Router error:', error)
  // 如果是状态相关的错误，尝试重新导航到当前路径
  if (error.message && error.message.includes('state')) {
    console.warn('Router state error detected, attempting recovery')
    // 不进行自动重定向，避免无限循环
  }
})

// 添加导航守卫来确保路由状态正确
router.beforeEach((to, from, next) => {
  try {
    // 只检查路径是否存在，不强制要求 name
    if (to.path) {
      next()
    } else {
      console.warn('Invalid route detected:', to)
      next('/')
    }
  } catch (error) {
    console.error('Navigation guard error:', error)
    next('/')
  }
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(Antd)

// 然后加载Ant Design CSS
import 'ant-design-vue/dist/antd.css'

app.mount('#app')

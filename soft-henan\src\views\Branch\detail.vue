<template>
  <ContentDetail 
    :detail="branchDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'

const route = useRoute()
const loading = ref(false)
const branchDetail = ref({})

// 详情配置
const detailConfig = computed(() => ({
  // 基础字段配置
  idField: 'id',
  titleField: 'name',
  contentField: 'content',
  attachmentField: 'attachments',
  
  // 元数据字段（顶部显示）
  metaFields: [
    { key: 'publishDate', label: '发布时间', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'viewCount', label: '浏览量' }
  ],
  
  // 信息区域配置（左侧蓝色边框区域）
  infoSection: {
    fields: [
      { key: 'director', label: '负责人' },
      { key: 'establishTime', label: '成立时间' },
      { key: 'contactPhone', label: '联系电话' },
      { key: 'address', label: '办公地址' },
      { key: 'email', label: '电子邮箱' }
    ]
  },
  
  // 其他配置
  showActions: false, // 不显示底部的返回按钮，因为已经有BackToList组件了
  emptyText: '未找到分支机构信息',
  containerClass: 'branch-detail-container'
}))

// 获取分支机构详情
const fetchBranchDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    if (!id) {
      message.error('缺少分支机构ID参数')
      return
    }

    const res = await getContentDetail(id)
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      // 转换数据格式以适配ContentDetail组件
      branchDetail.value = {
        id: res.data.contentId,
        name: res.data.title,
        publishDate: formatDate(res.data.publishTime || res.data.createTime),
        source: res.data.source || '本会',
        author: res.data.author || '管理员',
        viewCount: res.data.pageViewCount || 0,
        isNew: res.data.isNew || false,
        director: res.data.director || '待定',
        establishTime: res.data.establishTime || '待定',
        contactPhone: res.data.contactPhone || '待定',
        address: res.data.address || '待定',
        email: res.data.email || '待定',
        content: res.data.contentHtml || res.data.description || '',
        attachments: res.data.attachments || []
      }
    } else {
      message.error('获取分支机构详情失败')
    }
  } catch (error) {
    console.error('获取分支机构详情失败:', error)
    message.error('获取分支机构详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  // 如果日期格式为 yyyy-MM-dd HH:mm:ss，则只取日期部分
  if (dateString.includes(' ')) {
    return dateString.split(' ')[0]
  }
  const date = new Date(dateString)
  if (!isNaN(date.getTime())) {
    return date.toLocaleDateString('zh-CN')
  }
  return dateString
}

// 页面加载时获取分支机构详情
onMounted(() => {
  fetchBranchDetail()
})
</script>

<style scoped>
.branch-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
}
</style>

<template>
  <div class="test-menu-page">
    <h1>菜单功能测试页面</h1>
    
    <div class="test-section">
      <h2>菜单数据状态</h2>
      <div class="status-info">
        <p><strong>菜单加载状态:</strong> {{ menuLoading ? '加载中' : '已加载' }}</p>
        <p><strong>菜单数据是否可用:</strong> {{ menuLoaded ? '是' : '否' }}</p>
        <p><strong>顶级菜单数量:</strong> {{ topMenuList.length }}</p>
        <p><strong>总菜单数量:</strong> {{ menuList.length }}</p>
      </div>
    </div>

    <div class="test-section">
      <h2>顶级菜单列表</h2>
      <div class="menu-list">
        <div v-for="menu in topMenuList" :key="menu.menuId" class="menu-item">
          <div class="menu-info">
            <span class="menu-name">{{ menu.menuName }}</span>
            <span class="menu-type">({{ menu.menuType === 1 ? '目录' : '菜单' }})</span>
            <span class="menu-path">{{ menu.path || '无路径' }}</span>
          </div>
          <div v-if="menu.menuType === 1" class="sub-menu-list">
            <div v-for="subMenu in getChildrenMenus(menu.menuId)" :key="subMenu.menuId" class="sub-menu-item">
              <span class="sub-menu-name">{{ subMenu.menuName }}</span>
              <span class="sub-menu-path">{{ subMenu.path || '无路径' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>所有菜单数据</h2>
      <pre class="menu-data">{{ JSON.stringify(menuList, null, 2) }}</pre>
    </div>

    <div class="test-section">
      <h2>操作按钮</h2>
      <div class="action-buttons">
        <a-button @click="reloadMenu" :loading="menuLoading">重新加载菜单</a-button>
        <a-button @click="clearMenu">清空菜单</a-button>
        <a-button @click="setDefaultMenu">设置默认菜单</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAppStore } from '../store/modules/appStore'

const appStore = useAppStore()

// 获取菜单数据
const menuList = computed(() => appStore.menuList)
const topMenuList = computed(() => appStore.getTopMenuList)
const menuLoaded = computed(() => appStore.menuLoaded)
const menuLoading = computed(() => appStore.menuLoading)

// 获取子菜单
const getChildrenMenus = (parentId) => {
  return appStore.getChildrenMenus(parentId)
}

// 重新加载菜单
const reloadMenu = async () => {
  await appStore.loadMenuList()
}

// 清空菜单
const clearMenu = () => {
  appStore.menuList = []
  appStore.topMenuList = []
  appStore.menuLoaded = false
}

// 设置默认菜单
const setDefaultMenu = () => {
  appStore.setDefaultMenuData()
}
</script>

<style scoped>
.test-menu-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.test-section h2 {
  margin-bottom: 15px;
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 5px;
}

.status-info p {
  margin: 8px 0;
  font-size: 14px;
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.menu-item {
  padding: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
}

.menu-info {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 5px;
}

.menu-name {
  font-weight: bold;
  color: #1890ff;
}

.menu-type {
  color: #666;
  font-size: 12px;
}

.menu-path {
  color: #999;
  font-size: 12px;
  font-family: monospace;
}

.sub-menu-list {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 2px solid #e8e8e8;
}

.sub-menu-item {
  padding: 5px 0;
  display: flex;
  gap: 10px;
  align-items: center;
}

.sub-menu-name {
  font-weight: 500;
  color: #333;
}

.sub-menu-path {
  color: #999;
  font-size: 12px;
  font-family: monospace;
}

.menu-data {
  background-color: #f6f8fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons .ant-btn {
  margin-right: 10px;
}
</style>

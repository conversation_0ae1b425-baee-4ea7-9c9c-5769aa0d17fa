/*
 * 内容分类管理 API
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const contentCategoryApi = {
  /**
   * 添加内容分类
   */
  addCategory: (param) => {
    return postRequest('/website/contentCategory/add', param);
  },

  /**
   * 更新内容分类
   */
  updateCategory: (param) => {
    return postRequest('/website/contentCategory/update', param);
  },

  /**
   * 删除内容分类
   */
  deleteCategory: (categoryId) => {
    return getRequest(`/website/contentCategory/delete/${categoryId}`);
  },

  /**
   * 获取内容分类详情
   */
  getCategoryDetail: (categoryId) => {
    return getRequest(`/website/contentCategory/get/${categoryId}`);
  },

  /**
   * 查询内容分类树
   */
  getCategoryTree: (queryForm) => {
    return postRequest('/website/contentCategory/tree', queryForm);
  },
};

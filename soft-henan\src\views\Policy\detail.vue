<template>
  <ContentDetail 
    :detail="policyDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'

const route = useRoute()
const loading = ref(false)
const policyDetail = ref({})

// 详情配置
const detailConfig = computed(() => ({
  // 基础字段配置
  idField: 'contentId',
  titleField: 'title',
  summaryField: 'summary',
  contentField: 'contentHtml',
  attachmentField: 'attachment',
  
  // 元数据字段（顶部显示）
  metaFields: [
    { key: 'contentTypeName', label: '分类' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'pageViewCount', label: '浏览量' }
  ],
  
  // 信息区域配置（左侧蓝色边框区域）
  infoSection: {
    fields: [
      { key: 'contentTypeName', label: '分类' },
      { key: 'author', label: '作者' },
      { key: 'source', label: '来源' },
      { key: 'publishTime', label: '发布时间', type: 'date' },
      { key: 'pageViewCount', label: '浏览量' }
    ]
  },
  
  // 摘要配置
  summaryTitle: '政策摘要',
  
  // 其他配置
  showActions: false, // 不显示底部的返回按钮，因为已经有BackToList组件了
  emptyText: '政策内容不存在或已被删除',
  containerClass: 'policy-detail-container'
}))

// 页面加载
onMounted(() => {
  const contentId = route.query.id || route.params.id
  if (contentId) {
    queryPolicyDetail(contentId)
  } else {
    message.error('缺少政策内容ID')
  }
})

// 查询政策详情
const queryPolicyDetail = async (contentId) => {
  try {
    loading.value = true
    const result = await getContentDetail(contentId)
    
    if (result && result.data) {
      policyDetail.value = result.data
    } else {
      policyDetail.value = {}
      message.error('获取政策详情失败')
    }
  } catch (error) {
    console.error('获取政策详情失败:', error)
    message.error('获取政策详情失败')
    policyDetail.value = {}
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.policy-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}
</style>

<style>
.policy-content img {
  max-width: 100%;
  height: auto;
}

.policy-content p {
  margin-bottom: 15px;
}

.policy-content h1,
.policy-content h2,
.policy-content h3,
.policy-content h4,
.policy-content h5,
.policy-content h6 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #333;
}
</style>



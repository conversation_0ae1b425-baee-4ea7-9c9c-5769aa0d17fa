<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.website.content.dao.ContentCategoryRelationDao">

    <!-- 根据内容ID查询分类ID列表 -->
    <select id="selectCategoryIdsByContentId" resultType="java.lang.Long">
        SELECT category_id FROM t_website_content_category_relation
        WHERE content_id = #{contentId}
    </select>

    <!-- 根据分类ID查询内容ID列表 -->
    <select id="selectContentIdsByCategoryId" resultType="java.lang.Long">
        SELECT content_id FROM t_website_content_category_relation
        WHERE category_id = #{categoryId}
    </select>

    <!-- 批量插入内容分类关联 -->
    <insert id="batchInsert">
        INSERT INTO t_website_content_category_relation
        (content_id, category_id, create_time)
        VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{contentId}, #{categoryId}, NOW())
        </foreach>
    </insert>

    <!-- 根据内容ID删除关联 -->
    <delete id="deleteByContentId">
        DELETE FROM t_website_content_category_relation
        WHERE content_id = #{contentId}
    </delete>

</mapper>

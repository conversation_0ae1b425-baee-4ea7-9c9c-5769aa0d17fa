package net.lingyue.ly.admin.module.website.content.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容类型VO
 */
@Data
public class ContentTypeVO {

    private Long contentTypeId;

    /**
     * 类型名称
     */
    private String contentTypeName;

    /**
     * 类型编码
     */
    private String contentTypeCode;

    /**
     * 类型描述
     */
    private String contentTypeDesc;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否启用
     */
    private Boolean enableFlag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}

<template>
  <div class="registration-container">
    <div class="registration-banner">
      <div class="banner-content">
        <div class="banner-title-area">
          <h1>河南省软组织病研究会</h1>
          <h2 class="meeting-title">学术交流会议报名</h2>
        </div>
        <div class="meeting-notice">
          <span class="notice-icon">i</span>
          <span>为确保会议顺利进行，请务必填写真实信息</span>
        </div>
      </div>
    </div>

    <div class="main-content">
      <div class="form-container">
        <div class="form-header">
          <i class="form-icon"></i>
          <span>填写报名信息</span>
        </div>
        
        <!-- 步骤条 -->
        <div class="form-steps">
          <a-steps :current="currentStep" size="small" :responsive="false">
            <a-step title="基本信息" />
            <a-step title="参会信息" />
            <a-step title="费用信息" />
          </a-steps>
        </div>
        
        <a-form
          :model="formState"
          name="registration"
          layout="vertical"
          autocomplete="off"
          @finish="onFinish"
          class="custom-form"
        >
          <!-- 步骤1：基本信息 -->
          <div v-show="currentStep === 0">
            <div class="form-section">
              <div class="section-title">
                <span class="section-icon">👤</span>
                <span>基本信息</span>
              </div>
              
              <a-form-item
                label="姓名"
                name="name"
                :rules="[{ required: true, message: '请输入姓名' }]"
              >
                <a-input 
                  v-model:value="formState.name" 
                  placeholder="请输入您的真实姓名" 
                  :maxLength="20"
                />
              </a-form-item>

              <a-form-item
                label="手机号码"
                name="phone"
                :rules="[
                  { required: true, message: '请输入手机号码' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                ]"
              >
                <a-input 
                  v-model:value="formState.phone" 
                  placeholder="用于接收会议通知" 
                  :maxLength="11"
                />
              </a-form-item>

              <a-form-item
                label="医院/单位"
                name="hospital"
                :rules="[{ required: true, message: '请输入医院或单位名称' }]"
              >
                <a-input 
                  v-model:value="formState.hospital" 
                  placeholder="请输入您所在的医院或单位"
                  :maxLength="50" 
                />
              </a-form-item>
            </div>
          </div>

          <!-- 步骤2：参会信息 -->
          <div v-show="currentStep === 1">
            <div class="form-section">
              <div class="section-title">
                <span class="section-icon">📅</span>
                <span>参会信息</span>
              </div>
              
              <a-form-item
                label="参会人数"
                name="attendees"
                :rules="[{ required: true, message: '请选择参会人数' }]"
              >
                <div class="attendee-selector">
                  <a-input-number 
                    v-model:value="formState.attendees" 
                    :min="1" 
                    :max="20"
                    @change="calculateFee" 
                    class="attendee-input" 
                  />
                  <div class="attendee-buttons">
                    <a-button 
                      class="attendee-btn" 
                      @click="() => {if(formState.attendees > 1) {formState.attendees--; calculateFee();}}"
                      :disabled="formState.attendees <= 1"
                    >
                      <span>-</span>
                    </a-button>
                    <a-button 
                      class="attendee-btn" 
                      @click="() => {formState.attendees++; calculateFee();}"
                    >
                      <span>+</span>
                    </a-button>
                  </div>
                </div>
              </a-form-item>

              <a-form-item
                v-if="formState.attendees > 1"
                label="团队成员"
                name="teamMembers"
              >
                <a-textarea
                  v-model:value="formState.teamMembers"
                  :rows="3"
                  placeholder="请输入其他参会人员的姓名、电话，每人一行"
                />
              </a-form-item>

              <a-form-item
                label="参会时间"
                name="time"
                :rules="[{ required: true, message: '请选择参会时间' }]"
              >
                <a-date-picker
                  v-model:value="formState.time"
                  style="width: 100%"
                  :disabled-date="disabledDate"
                  placeholder="请选择参加会议的日期"
                />
              </a-form-item>
              

              <a-form-item
                label="特殊需求"
                name="specialNeeds"
              >
                <a-textarea
                  v-model:value="formState.specialNeeds"
                  :rows="2"
                  placeholder="可选，如有饮食禁忌或其他需求请在此说明"
                />
              </a-form-item>
            </div>
          </div>

          <!-- 步骤3：费用信息 -->
          <div v-show="currentStep === 2">
            <div class="form-section">
              <div class="section-title">
                <span class="section-icon">💰</span>
                <span>费用信息</span>
              </div>

              <div class="fee-card">
                <div class="fee-item">
                  <div class="fee-info">
                    <span class="fee-label">参会基本费用：</span>
                    <div class="fee-detail">每人 ¥{{ feePerPerson }}元 × {{ formState.attendees }}人</div>
                  </div>
                  <div class="fee-value">¥{{ (feePerPerson * formState.attendees).toFixed(2) }}</div>
                </div>
                
                <div class="fee-item" v-if="formState.dinner">
                  <div class="fee-info">
                    <span class="fee-label">晚宴费用：</span>
                    <div class="fee-detail">每人 ¥{{ dinnerFee }}元 × {{ formState.attendees }}人</div>
                  </div>
                  <div class="fee-value">¥{{ (dinnerFee * formState.attendees).toFixed(2) }}</div>
                </div>
                
                <div class="fee-divider"></div>
                
                <div class="fee-total">
                  <span>应付总额：</span>
                  <span class="fee-amount">¥{{ totalFee.toFixed(2) }}</span>
                </div>
              </div>

              <a-form-item
                label="发票信息"
                name="invoice"
              >
                <div class="invoice-selection">
                  <div 
                    :class="['invoice-option', {'active': !formState.needInvoice}]" 
                    @click="() => {formState.needInvoice = false; handleInvoiceChange()}"
                  >
                    <div class="invoice-icon">🚫</div>
                    <div>不需要发票</div>
                  </div>
                  <div 
                    :class="['invoice-option', {'active': formState.needInvoice}]" 
                    @click="() => {formState.needInvoice = true; handleInvoiceChange()}"
                  >
                    <div class="invoice-icon">🧾</div>
                    <div>需要发票</div>
                  </div>
                </div>
              </a-form-item>

              <a-form-item
                v-if="formState.needInvoice"
                label="发票抬头"
                name="invoiceTitle"
                :rules="[
                  { required: formState.needInvoice, message: '请输入发票抬头' }
                ]"
              >
                <a-input v-model:value="formState.invoiceTitle" placeholder="请输入发票抬头" />
              </a-form-item>

              <a-form-item
                v-if="formState.needInvoice"
                label="纳税人识别号"
                name="taxNumber"
              >
                <a-input v-model:value="formState.taxNumber" placeholder="请输入纳税人识别号" />
              </a-form-item>
              
              <div class="form-agreement">
                <a-checkbox v-model:checked="formState.agreeTerms">
                  我已阅读并同意<a href="javascript:void(0)" @click="showTerms">《参会须知与条款》</a>
                </a-checkbox>
              </div>
            </div>
          </div>

          <div class="form-navigation">
            <a-button 
              v-if="currentStep > 0" 
              @click="prevStep"
              class="prev-btn"
            >
              <span class="btn-icon">←</span>
              上一步
            </a-button>
            <a-button 
              v-if="currentStep < 2" 
              type="primary" 
              @click="nextStep"
              class="next-btn"
            >
              下一步
              <span class="btn-icon">→</span>
            </a-button>
            <a-button
              v-if="currentStep === 2"
              type="primary"
              html-type="submit"
              :loading="loading"
              :disabled="!formState.agreeTerms"
              class="submit-btn"
            >
              提交并支付
            </a-button>
          </div>
        </a-form>
      </div>
    </div>

    <div class="footer-info">
      <div class="info-item">
        <i class="info-icon location-icon"></i>
        <span><strong>会议地点：</strong>郑州市XXXX酒店（XXXX路XX号）</span>
      </div>
      <div class="info-item">
        <i class="info-icon phone-icon"></i>
        <span><strong>联系方式：</strong>0371-XXXXXXXX（赵老师）</span>
      </div>
      <div class="info-item">
        <i class="info-icon org-icon"></i>
        <span><strong>主办单位：</strong>河南省软组织病研究会</span>
      </div>
    </div>
    
    <!-- 条款对话框 -->
    <a-modal v-model:visible="termsVisible" title="参会须知与条款" @ok="termsVisible = false">
      <div class="terms-content">
        <h3>一、报名须知</h3>
        <p>1. 请确保提供的个人信息真实有效，以便会务组准确安排会议相关事宜。</p>
        <p>2. 报名成功后，会务组将通过短信向您发送确认信息。</p>
        <p>3. 会议资料将于会议当天发放，请凭报名确认短信签到领取。</p>
        
        <h3>二、付款与发票</h3>
        <p>1. 报名费用包含会议资料、茶歇等，住宿费用自理。</p>
        <p>2. 如需发票，请在报名时提供准确的开票信息。</p>
        <p>3. 发票将在会议结束后一个月内寄出或发送电子版。</p>
        
        <h3>三、退款政策</h3>
        <p>1. 会议开始前7天以上取消报名，全额退款。</p>
        <p>2. 会议开始前3-7天取消报名，退款50%。</p>
        <p>3. 会议开始前3天内取消报名，不予退款。</p>
        
        <h3>四、其他规定</h3>
        <p>1. 会议期间，请遵守会场秩序，保持安静。</p>
        <p>2. 如有特殊情况需要离场，请提前告知会务人员。</p>
        <p>3. 主办方保留对会议议程调整的权利。</p>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { submitRegistration, getActivityFeeInfo } from '../../api/registration'

const router = useRouter()
const loading = ref(false)
const feePerPerson = ref(300) // 默认每人300元
const dinnerFee = ref(200) // 晚宴费用
const totalFee = ref(300)
const termsVisible = ref(false)
const currentStep = ref(0) // 当前步骤

const formState = reactive({
  name: '',
  phone: '',
  hospital: '',
  attendees: 1,
  teamMembers: '',
  time: null,
  dinner: false,
  specialNeeds: '',
  needInvoice: false,
  invoiceTitle: '',
  taxNumber: '',
  agreeTerms: false
})

onMounted(() => {
  // 获取活动费用信息
  fetchActivityFeeInfo()
})

const fetchActivityFeeInfo = async () => {
  try {
    const res = await getActivityFeeInfo()
    if (res && res.data) {
      feePerPerson.value = res.data.feePerPerson || 300
      if (res.data.dinnerFee) {
        dinnerFee.value = res.data.dinnerFee
      }
      calculateFee()
    }
  } catch (error) {
    console.error('获取费用信息失败', error)
    // 使用默认费用
  }
}

const calculateFee = () => {
  let fee = formState.attendees * feePerPerson.value
  if (formState.dinner) {
    fee += formState.attendees * dinnerFee.value
  }
  totalFee.value = fee
}

const handleInvoiceChange = () => {
  if (!formState.needInvoice) {
    formState.invoiceTitle = ''
    formState.taxNumber = ''
  }
}

const disabledDate = (current) => {
  // 禁用今天之前的日期
  return current && current < dayjs().startOf('day')
}

const showTerms = () => {
  termsVisible.value = true
}

// 下一步
const nextStep = async () => {
  // 基本信息验证
  if (currentStep.value === 0) {
    if (!formState.name) {
      message.warning('请输入姓名')
      return
    }
    if (!formState.phone) {
      message.warning('请输入手机号码')
      return
    }
    if (!/^1[3-9]\d{9}$/.test(formState.phone)) {
      message.warning('请输入正确的手机号码')
      return
    }
    if (!formState.hospital) {
      message.warning('请输入医院或单位名称')
      return
    }
  }
  
  // 参会信息验证  
  if (currentStep.value === 1) {
    if (!formState.time) {
      message.warning('请选择参会时间')
      return
    }
  }
  
  currentStep.value++
}

// 上一步
const prevStep = () => {
  currentStep.value--
}

const onFinish = async () => {
  if (!formState.agreeTerms) {
    message.warning('请阅读并同意参会须知与条款')
    return
  }
  
  loading.value = true
  try {
    // 提交报名信息到后端
    const registrationData = {
      name: formState.name,
      phone: formState.phone,
      hospital: formState.hospital,
      attendees: formState.attendees,
      teamMembers: formState.teamMembers || '',
      time: formState.time.format('YYYY-MM-DD'),
      dinner: formState.dinner,
      specialNeeds: formState.specialNeeds || '',
      needInvoice: formState.needInvoice,
      invoiceTitle: formState.invoiceTitle || '',
      taxNumber: formState.taxNumber || '',
      totalFee: totalFee.value
    }
    
    const res = await submitRegistration(registrationData)
    
    if (res && res.code === 1) {
      // 存储报名数据和订单ID到本地存储
      localStorage.setItem('registrationData', JSON.stringify({
        ...registrationData,
        orderId: res.data.orderId,
      }))
      
      // 跳转到支付页面
      router.push('/registration/payment')
    } else {
      message.error(res.msg || '提交失败，请稍后重试')
    }
  } catch (error) {
    console.error('提交报名信息失败', error)
    message.error('提交失败，请稍后重试')
    
    // 在开发环境下，如果API不可用，仍然允许进入下一步
    if (import.meta.env.DEV) {
      localStorage.setItem('registrationData', JSON.stringify({
        ...formState,
        time: formState.time.format('YYYY-MM-DD'),
        teamMembers: formState.teamMembers || '',
        dinner: formState.dinner,
        specialNeeds: formState.specialNeeds || '',
        needInvoice: formState.needInvoice,
        invoiceTitle: formState.invoiceTitle || '',
        taxNumber: formState.taxNumber || '',
        totalFee: totalFee.value,
        orderId: 'DEV-ORDER-' + Math.floor(Math.random() * 1000000)
      }))
      router.push('/registration/payment')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.registration-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0;
  background-color: #f5f7fa;
  color: #333;
}

.registration-banner {
  background: linear-gradient(135deg, #2b87d9, #0050b3);
  color: white;
  padding: 15px 0;
  text-align: center;
  margin-bottom: 16px;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 10px rgba(0, 65, 145, 0.2);
}

.banner-content {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.banner-title-area {
  margin-bottom: 8px;
  position: relative;
  padding-top: 5px;
}

.banner-title-area::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
}

.registration-banner h1 {
  font-size: 20px;
  margin-bottom: 2px;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.meeting-title {
  font-size: 16px;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.meeting-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 5px 12px;
  margin: 5px auto 0;
  max-width: 340px;
  line-height: 1.3;
}

.notice-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: #fff;
  color: #1890ff;
  border-radius: 50%;
  font-weight: bold;
  font-style: italic;
  margin-right: 6px;
  font-size: 11px;
}

.main-content {
  padding: 0 16px;
}

.form-container {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.form-header {
  padding: 16px 20px;
  background-color: #f0f7ff;
  border-bottom: 1px solid #e6f0fa;
  font-size: 18px;
  font-weight: 500;
  color: #1677ff;
  display: flex;
  align-items: center;
}

.form-steps {
  padding: 15px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.form-icon {
  margin-right: 8px;
}

.custom-form {
  padding: 24px 20px;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 24px;
  border-bottom: none;
}

.section-title {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.section-icon {
  margin-right: 10px;
  font-size: 20px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 8px;
}

:deep(.ant-form-item-label > label) {
  color: #444;
  font-weight: 500;
}

:deep(.ant-input), 
:deep(.ant-input-number), 
:deep(.ant-picker) {
  border-radius: 8px;
  padding: 10px 12px;
  height: auto;
  border: 1px solid #d9d9d9;
}

:deep(.ant-input:focus), 
:deep(.ant-input-number:focus), 
:deep(.ant-picker:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.attendee-selector {
  display: flex;
  align-items: center;
}

.attendee-input {
  flex: 1;
  border-radius: 8px;
}

.attendee-buttons {
  display: flex;
  margin-left: 8px;
}

.attendee-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  border-radius: 8px;
  font-size: 18px;
  padding: 0;
}

.toggle-container {
  display: flex;
  align-items: center;
}

.toggle-label {
  margin: 0 8px;
  color: #666;
}

.toggle-label.active {
  color: #1890ff;
  font-weight: 500;
}

.fee-card {
  background-color: #f9fbff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.fee-info {
  display: flex;
  flex-direction: column;
}

.fee-label {
  color: #666;
  margin-bottom: 4px;
}

.fee-detail {
  color: #888;
  font-size: 13px;
}

.fee-value {
  font-weight: 500;
  color: #333;
}

.fee-divider {
  height: 1px;
  background-color: #eaeaea;
  margin: 12px 0;
}

.fee-total {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
}

.fee-amount {
  font-size: 20px;
  color: #f5222d;
  font-weight: 600;
}

.invoice-selection {
  display: flex;
  gap: 12px;
}

.invoice-option {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.invoice-option.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
  color: #1890ff;
}

.invoice-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.form-agreement {
  margin-bottom: 24px;
  text-align: center;
}

.form-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.form-navigation button {
  min-width: 110px;
  height: 38px;
  border-radius: 19px;
  font-weight: 500;
  font-size: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.next-btn, .submit-btn {
  background: linear-gradient(135deg, #2b87d9, #0050b3);
  border: none;
}

.next-btn:hover, .submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #3a96e8, #0067e6);
}

.prev-btn {
  background: #fff;
  color: #666;
  border: 1px solid #ddd;
}

.prev-btn:hover {
  color: #2b87d9;
  border-color: #2b87d9;
}

.btn-icon {
  font-size: 14px;
  margin: 0 4px;
}

.footer-info {
  background-color: #fff;
  padding: 20px;
  text-align: center;
  border-radius: 12px 12px 0 0;
  margin-top: 40px;
  border-top: 1px solid #eaeaea;
}

.info-item {
  margin-bottom: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-icon {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.terms-content {
  max-height: 400px;
  overflow-y: auto;
}

.terms-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #1890ff;
}

.terms-content p {
  margin-bottom: 8px;
  line-height: 1.6;
  font-size: 14px;
  color: #666;
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .registration-banner {
    padding: 30px 15px;
  }
  
  .registration-banner h1 {
    font-size: 22px;
  }
  
  .meeting-title {
    font-size: 18px;
  }
  
  .main-content {
    padding: 0 12px;
  }
  
  .custom-form {
    padding: 16px 15px;
  }
  
  .section-title {
    font-size: 16px;
  }

  .invoice-selection {
    flex-direction: column;
    gap: 8px;
  }

  .form-navigation {
    padding: 0 15px 15px;
  }
}

:deep(.ant-steps) {
  max-width: 500px;
  margin: 0 auto;
}

:deep(.ant-steps-item-title) {
  font-size: 14px;
}

@media (max-width: 480px) {
  :deep(.ant-steps-item-title) {
    font-size: 12px;
  }
  
  :deep(.ant-steps-item-icon) {
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin-right: 6px;
  }

  .form-navigation button {
    min-width: 100px;
    font-size: 14px;
  }
}
</style> 
package net.lingyue.ly.admin.module.website.config.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lingyue.ly.base.common.domain.PageParam;


/**
 * 网站配置查询表单
 */
@Data
public class WebsiteConfigQueryForm extends PageParam {

    @Schema(description = "配置键")
    private String configKey;

    @Schema(description = "配置名称")
    private String configName;
}

package net.lingyue.ly.admin.module.website.content.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentAddForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentUpdateForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentDetailVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentTypeVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentUpdateFormVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentVO;
import net.lingyue.ly.admin.module.website.content.service.ContentService;
import net.lingyue.ly.admin.module.website.content.service.ContentTypeService;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartRequestUtil;
import net.lingyue.ly.base.module.support.operatelog.annotation.OperateLog;
import net.lingyue.ly.base.module.support.repeatsubmit.annoation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 内容管理
 */
@Tag(name = AdminSwaggerTagConst.Website.CONTENT)
@RestController
@OperateLog
public class ContentController {

    @Resource
    private ContentService contentService;

    @Resource
    private ContentTypeService contentTypeService;

    // --------------------- 内容类型 -------------------------

    @Operation(summary = "内容类型-获取全部 @author")
    @GetMapping("/website/contentType/getAll")
    public ResponseDTO<List<ContentTypeVO>> getAllContentTypes() {
        return ResponseDTO.ok(contentTypeService.getAll());
    }

    @Operation(summary = "内容类型-添加 @author")
    @PostMapping("/website/contentType/add")
    @SaCheckPermission("website:content:type:add")
    public ResponseDTO<String> addContentType(@RequestParam String contentTypeName, 
                                             @RequestParam String contentTypeCode,
                                             @RequestParam(required = false) String contentTypeDesc,
                                             @RequestParam(required = false, defaultValue = "0") Integer sort) {
        return contentTypeService.add(contentTypeName, contentTypeCode, contentTypeDesc, sort);
    }

    @Operation(summary = "内容类型-修改 @author")
    @PostMapping("/website/contentType/update")
    @SaCheckPermission("website:content:type:update")
    public ResponseDTO<String> updateContentType(@RequestParam Long contentTypeId, 
                                               @RequestParam String contentTypeName,
                                               @RequestParam(required = false) String contentTypeDesc,
                                               @RequestParam(required = false, defaultValue = "0") Integer sort) {
        return contentTypeService.update(contentTypeId, contentTypeName, contentTypeDesc, sort);
    }

    @Operation(summary = "内容类型-启用/禁用 @author")
    @GetMapping("/website/contentType/updateStatus")
    @SaCheckPermission("website:content:type:update")
    public ResponseDTO<String> updateContentTypeStatus(@RequestParam Long contentTypeId, @RequestParam Boolean enableFlag) {
        return contentTypeService.updateStatus(contentTypeId, enableFlag);
    }

    @Operation(summary = "内容类型-删除 @author")
    @GetMapping("/website/contentType/delete/{contentTypeId}")
    @SaCheckPermission("website:content:type:delete")
    public ResponseDTO<String> deleteContentType(@PathVariable Long contentTypeId) {
        return contentTypeService.delete(contentTypeId);
    }

    // --------------------- 内容管理 -------------------------

    @Operation(summary = "内容-分页查询 @author")
    @PostMapping("/website/content/query")
    @SaCheckPermission("website:content:query")
    public ResponseDTO<PageResult<ContentVO>> queryContent(@RequestBody @Valid ContentQueryForm queryForm) {
        return ResponseDTO.ok(contentService.query(queryForm));
    }

    @Operation(summary = "内容-添加 @author")
    @PostMapping("/website/content/add")
    @RepeatSubmit
    @SaCheckPermission("website:content:add")
    public ResponseDTO<String> addContent(@RequestBody @Valid ContentAddForm addForm) {
        addForm.setCreateUserId(SmartRequestUtil.getRequestUserId());
        return contentService.add(addForm);
    }

    @Operation(summary = "内容-更新 @author")
    @PostMapping("/website/content/update")
    @RepeatSubmit
    @SaCheckPermission("website:content:update")
    public ResponseDTO<String> updateContent(@RequestBody @Valid ContentUpdateForm updateForm) {
        return contentService.update(updateForm);
    }

    @Operation(summary = "内容-更新详情 @author")
    @GetMapping("/website/content/getUpdateVO/{contentId}")
    @SaCheckPermission("website:content:update")
    public ResponseDTO<ContentUpdateFormVO> getContentUpdateFormVO(@PathVariable Long contentId) {
        return ResponseDTO.ok(contentService.getUpdateFormVO(contentId));
    }

    @Operation(summary = "内容-详情 @author")
    @GetMapping("/website/content/getDetail/{contentId}")
    @SaCheckPermission("website:content:query")
    public ResponseDTO<ContentDetailVO> getContentDetail(@PathVariable Long contentId) {
        return ResponseDTO.ok(contentService.getDetail(contentId));
    }

    @Operation(summary = "内容-删除 @author")
    @GetMapping("/website/content/delete/{contentId}")
    @SaCheckPermission("website:content:delete")
    public ResponseDTO<String> deleteContent(@PathVariable Long contentId) {
        return contentService.delete(contentId);
    }
}

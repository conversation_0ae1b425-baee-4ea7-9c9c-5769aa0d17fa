/**
 * 详情页面配置
 * 用于配置不同内容类型的详情展示
 */

// 新闻详情配置
export const newsDetailConfig = {
  idField: 'contentId',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  attachmentField: 'attachment',
  containerClass: 'news-detail-container',
  emptyText: '未找到新闻',
  backButtonText: '返回列表',
  metaFields: [
    { key: 'contentTypeName', label: '分类' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'pageViewCount', label: '浏览量' }
  ]
}

// 活动详情配置
export const activityDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  summaryTitle: '活动简介',
  attachmentField: 'attachments',
  containerClass: 'activity-detail-container',
  emptyText: '未找到活动信息',
  backButtonText: '返回列表',
  infoSection: {
    fields: [
      { key: 'time', label: '活动时间' },
      { key: 'location', label: '活动地点' },
      { key: 'organizer', label: '主办单位' },
      { key: 'contact', label: '联系方式' }
    ]
  }
}

// 政策法规详情配置
export const policyDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  attachmentField: 'attachment',
  containerClass: 'policy-detail-container',
  emptyText: '未找到政策信息',
  backButtonText: '返回政策法规',
  backHandler: (router) => {
    router.push('/policy')
  },
  metaFields: [
    { key: 'publishTime', label: '发布日期', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

// 党建动态详情配置
export const partyDetailConfig = {
  idField: 'contentId',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  attachmentField: 'attachment',
  containerClass: 'party-detail-container',
  emptyText: '未找到党建动态',
  backButtonText: '返回党建动态',
  backHandler: (router) => {
    router.push('/party')
  },
  metaFields: [
    { key: 'contentTypeName', label: '分类' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'pageViewCount', label: '浏览量' }
  ]
}

// 代表处详情配置
export const representativeDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'content',
  containerClass: 'representative-detail-page',
  emptyText: '暂无详细信息',
  backButtonText: '返回列表',
  metaFields: [
    { key: 'publishDate', label: '发布时间', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'viewCount', label: '浏览量', formatter: (value) => value || 0 }
  ],
  infoSection: {
    fields: [
      { key: 'director', label: '负责人' },
      { key: 'establishTime', label: '成立时间' },
      { key: 'contactPhone', label: '联系电话' },
      { key: 'address', label: '办公地址', formatter: (value) => value || '河南省郑州市金水区文化路97号' },
      { key: 'email', label: '电子邮箱', formatter: (value) => value || '<EMAIL>' }
    ]
  }
}

// 服务详情配置
export const serviceDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  attachmentField: 'attachment',
  containerClass: 'service-detail-container',
  emptyText: '未找到服务信息',
  backButtonText: '返回服务项目',
  metaFields: [
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

// 法规详情配置
export const regulationDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  attachmentField: 'attachment',
  containerClass: 'regulation-detail-container',
  emptyText: '未找到法规信息',
  backButtonText: '返回法规制度',
  metaFields: [
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

// 分会详情配置
export const branchDetailConfig = {
  idField: 'branchId',
  titleField: 'branchName',
  contentField: 'description',
  containerClass: 'branch-detail-container',
  emptyText: '未找到分会信息',
  backButtonText: '返回分会列表',
  infoSection: {
    fields: [
      { key: 'establishTime', label: '成立时间' },
      { key: 'memberCount', label: '会员数量' },
      { key: 'contactPerson', label: '联系人' },
      { key: 'contactPhone', label: '联系电话' },
      { key: 'address', label: '地址' }
    ]
  }
}

// 分会文章详情配置
export const branchArticleDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  attachmentField: 'attachment',
  containerClass: 'branch-article-detail-container',
  emptyText: '未找到文章信息',
  backButtonText: '返回列表',
  metaFields: [
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

// 统一获取配置的函数
export const getDetailConfig = (type) => {
  const configs = {
    news: newsDetailConfig,
    activity: activityDetailConfig,
    policy: policyDetailConfig,
    party: partyDetailConfig,
    representative: representativeDetailConfig,
    service: serviceDetailConfig,
    regulation: regulationDetailConfig,
    branch: branchDetailConfig,
    branchArticle: branchArticleDetailConfig
  }
  
  return configs[type] || newsDetailConfig
}

// 默认配置
export const defaultDetailConfig = {
  idField: 'id',
  titleField: 'title',
  contentField: 'contentHtml',
  summaryField: 'summary',
  attachmentField: 'attachment',
  containerClass: 'content-detail-container',
  emptyText: '未找到内容',
  backButtonText: '返回列表',
  metaFields: [
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'author', label: '作者' },
    { key: 'source', label: '来源' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

export default {
  newsDetailConfig,
  activityDetailConfig,
  policyDetailConfig,
  partyDetailConfig,
  representativeDetailConfig,
  serviceDetailConfig,
  regulationDetailConfig,
  branchDetailConfig,
  branchArticleDetailConfig,
  getDetailConfig,
  defaultDetailConfig
}
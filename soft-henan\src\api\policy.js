import api from './index';
import { getAllContentTypes, getContentDetail } from './content';

/**
 * 获取政策法规列表
 * @param {Object} params 查询参数
 * @param {number} [params.pageNum=1] 页码
 * @param {number} [params.pageSize=10] 每页条数
 * @returns {Promise<any>}
 */
export function getPolicyList(params = {}) {
  // 先获取所有内容类型
  return getAllContentTypes()
    .then(res => {
      console.log('获取内容类型结果:', res);

      if (res && res.data) {
        // 查找政策法规类型
        const policyType = res.data.find(type =>
          type.contentTypeCode === 'policy' ||
          type.contentTypeCode === 'policies' ||
          type.contentTypeName?.includes('政策') ||
          type.contentTypeName?.includes('法规')
        );

        console.log('找到的政策法规类型:', policyType);

        if (policyType) {
          // 使用内容类型ID查询内容
          const queryParams = {
            contentTypeId: policyType.contentTypeId,
            pageNum: Number(params.pageNum || 1),
            pageSize: Number(params.pageSize || 10),
            status: 1, // 已发布状态
            deletedFlag: false // 未删除
          };

          console.log('查询政策法规内容参数:', queryParams);

          return api.get('/portal/content/list', { params: queryParams })
            .then(contentRes => {
              console.log('获取政策法规内容结果:', contentRes);
              return contentRes;
            });
        } else {
          console.warn('未找到政策法规内容类型');
          return {
            code: 0,
            data: {
              list: [],
              total: 0
            },
            msg: '未找到政策法规内容类型'
          };
        }
      } else {
        console.error('获取内容类型失败');
        return {
          code: 1,
          data: {
            list: [],
            total: 0
          },
          msg: '获取内容类型失败'
        };
      }
    })
    .catch(error => {
      console.error('获取政策法规列表失败:', error);
      return {
        code: 1,
        data: {
          list: [],
          total: 0
        },
        msg: '获取政策法规列表失败'
      };
    });
}

/**
 * 获取政策法规详情
 * @param {number} id 政策法规ID
 * @returns {Promise<any>}
 */
export function getPolicyDetail(id) {
  console.log('获取政策法规详情, ID:', id);
  
  if (!id) {
    return Promise.resolve({
      code: 1,
      data: null,
      msg: '政策ID不能为空'
    });
  }

  // 使用内容管理API获取详情
  return getContentDetail(id)
    .then(res => {
      console.log('获取政策法规详情结果:', res);
      
      if (res && res.code === 0 && res.data) {
        // 格式化返回数据
        const policyDetail = {
          id: res.data.contentId,
          title: res.data.title,
          contentHtml: res.data.contentHtml,
          publishTime: res.data.publishTime || res.data.createTime,
          source: res.data.source || '原创',
          author: res.data.author || '管理员',
          pageViewCount: res.data.pageViewCount || 0,
          attachment: res.data.attachment || []
        };
        
        return {
          code: 0,
          data: policyDetail,
          msg: '获取成功'
        };
      } else {
        return {
          code: 1,
          data: null,
          msg: res?.msg || '获取政策详情失败'
        };
      }
    })
    .catch(error => {
      console.error('获取政策法规详情失败:', error);
      return {
        code: 1,
        data: null,
        msg: '获取政策法规详情失败'
      };
    });
}

/**
 * 获取热门政策法规
 * @param {number} limit 限制数量
 * @returns {Promise<any>}
 */
export function getHotPolicies(limit = 5) {
  return getPolicyList({ pageNum: 1, pageSize: limit })
    .then(res => {
      if (res && res.code === 0 && res.data && res.data.list) {
        // 按浏览量排序
        const sortedList = res.data.list.sort((a, b) => (b.pageViewCount || 0) - (a.pageViewCount || 0));
        return {
          code: 0,
          data: sortedList.slice(0, limit),
          msg: '获取成功'
        };
      }
      return res;
    });
}
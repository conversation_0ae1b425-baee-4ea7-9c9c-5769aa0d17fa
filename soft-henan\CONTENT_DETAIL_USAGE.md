# ContentDetail 组件使用说明

## 概述

`ContentDetail` 是一个通用的内容详情展示组件，通过配置可以动态展示不同类型的内容详情页面，包括活动、分支机构、政策、服务等。

## 主要特性

1. **统一布局**：所有详情页面使用相同的布局结构
2. **配置驱动**：通过配置对象动态控制显示内容
3. **灵活字段**：支持多种字段类型和格式化
4. **响应式设计**：适配不同屏幕尺寸
5. **主题集成**：自动使用应用主题色

## 配置选项

### 基础字段配置

```javascript
const detailConfig = {
  // 必填字段
  idField: 'id',           // 内容ID字段名
  titleField: 'title',     // 标题字段名
  
  // 可选字段
  summaryField: 'summary',           // 摘要字段名
  contentField: 'contentHtml',      // 内容字段名
  attachmentField: 'attachments',   // 附件字段名
}
```

### 元数据字段配置

```javascript
metaFields: [
  { key: 'time', label: '活动时间', type: 'datetime' },
  { key: 'location', label: '活动地点' },
  { key: 'organizer', label: '主办单位' },
  { key: 'contact', label: '联系方式' }
]
```

### 信息区域配置

```javascript
infoSection: {
  fields: [
    { key: 'director', label: '负责人' },
    { key: 'establishTime', label: '成立时间' },
    { key: 'contactPhone', label: '联系电话' },
    { key: 'address', label: '办公地址' },
    { key: 'email', label: '电子邮箱' }
  ]
}
```

### 其他配置

```javascript
{
  summaryTitle: '活动简介',           // 摘要标题
  showActions: false,               // 是否显示底部操作按钮
  emptyText: '未找到内容',          // 空数据提示文本
  containerClass: 'custom-class'    // 自定义容器样式类
}
```

## 字段类型支持

### 日期类型

```javascript
{ key: 'publishTime', label: '发布时间', type: 'date' }      // 仅日期
{ key: 'createTime', label: '创建时间', type: 'datetime' }   // 日期+时间
```

### 自定义格式化

```javascript
{ 
  key: 'status', 
  label: '状态', 
  formatter: (value) => {
    const statusMap = { 0: '草稿', 1: '已发布', 2: '已下线' }
    return statusMap[value] || '未知'
  }
}
```

## 使用示例

### 活动详情页面

```vue
<template>
  <ContentDetail 
    :detail="activityDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { computed } from 'vue'
import ContentDetail from '../../components/common/ContentDetail.vue'

const detailConfig = computed(() => ({
  idField: 'id',
  titleField: 'title',
  summaryField: 'summary',
  contentField: 'contentHtml',
  attachmentField: 'attachments',
  
  metaFields: [
    { key: 'time', label: '活动时间', type: 'datetime' },
    { key: 'location', label: '活动地点' },
    { key: 'organizer', label: '主办单位' },
    { key: 'contact', label: '联系方式' }
  ],
  
  infoSection: {
    fields: [
      { key: 'time', label: '活动时间', type: 'datetime' },
      { key: 'location', label: '活动地点' },
      { key: 'organizer', label: '主办单位' },
      { key: 'contact', label: '联系方式' }
    ]
  },
  
  summaryTitle: '活动简介',
  showActions: false,
  emptyText: '未找到活动信息',
  containerClass: 'activity-detail-container'
}))
</script>
```

### 分支机构详情页面

```javascript
const detailConfig = computed(() => ({
  idField: 'id',
  titleField: 'name',
  contentField: 'content',
  attachmentField: 'attachments',
  
  metaFields: [
    { key: 'publishDate', label: '发布时间', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'viewCount', label: '浏览量' }
  ],
  
  infoSection: {
    fields: [
      { key: 'director', label: '负责人' },
      { key: 'establishTime', label: '成立时间' },
      { key: 'contactPhone', label: '联系电话' },
      { key: 'address', label: '办公地址' },
      { key: 'email', label: '电子邮箱' }
    ]
  },
  
  showActions: false,
  emptyText: '未找到分支机构信息',
  containerClass: 'branch-detail-container'
}))
```

## 数据格式要求

### 输入数据格式

```javascript
const detail = {
  id: '123',
  title: '活动标题',
  time: '2025-01-01 10:00:00',
  location: '活动地点',
  organizer: '主办单位',
  contact: '联系方式',
  summary: '活动摘要',
  contentHtml: '<p>活动内容</p>',
  attachments: [
    { name: '附件1.pdf', url: '/files/1.pdf' },
    { name: '附件2.doc', url: '/files/2.doc' }
  ]
}
```

### 字段映射

组件会自动根据配置的字段名从数据对象中提取值：

- `metaFields` 中的字段会显示在顶部元数据区域
- `infoSection.fields` 中的字段会显示在左侧蓝色边框的信息区域
- `summaryField` 指定的字段会显示在摘要区域
- `contentField` 指定的字段会显示在主要内容区域
- `attachmentField` 指定的字段会显示在附件区域

## 样式定制

### 容器样式类

通过 `containerClass` 可以自定义容器的样式：

```javascript
containerClass: 'activity-detail-container'
```

```css
.activity-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
}
```

### 主题色集成

组件自动使用 CSS 变量中的主题色：

- `--primary-color`: 主要颜色（边框、链接等）
- `--primary-dark-color`: 深色主题色（悬停状态）

## 优势

1. **代码复用**：避免在每个详情页面重复编写相同的布局代码
2. **维护性**：统一的样式和逻辑，便于维护和更新
3. **一致性**：所有详情页面具有相同的视觉风格和交互体验
4. **灵活性**：通过配置可以轻松适配不同类型的内容
5. **扩展性**：可以轻松添加新的字段类型和格式化选项

## 注意事项

1. 确保传入的 `detail` 对象包含配置中指定的字段
2. 字段类型要正确配置，特别是日期类型
3. 自定义格式化函数要处理异常情况
4. 容器样式类要避免与组件内部样式冲突

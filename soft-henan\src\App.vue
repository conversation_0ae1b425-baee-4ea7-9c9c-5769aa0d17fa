<script setup>
import { onBeforeUnmount, onMounted } from 'vue'
import { useAppStore } from './store/modules/appStore'
import MainLayout from './components/layout/MainLayout.vue'
import { testApiConnection } from './utils/apiTest'

const appStore = useAppStore()

// 在组件挂载时初始化所有配置
onMounted(async () => {
  console.log('App组件挂载，开始初始化所有配置')
  
  // 测试API连接
  console.log('=== 开始API连接测试 ===')
  const apiTestResult = await testApiConnection()
  console.log('API测试结果:', apiTestResult)
  console.log('=== API连接测试结束 ===')
  
  try {
    await appStore.initAllConfig()
    console.log('所有配置初始化完成')
  } catch (error) {
    console.error('初始化配置失败:', error)
  }
})


</script>

<template>
  <MainLayout>
    <!-- 添加keep-alive支持 -->
    <router-view v-slot="{ Component, route }">
      <keep-alive>
        <component :is="Component" v-if="Component && route.meta?.keepAlive" :key="route.name" />
      </keep-alive>
      <component :is="Component" v-if="Component && !route.meta?.keepAlive" :key="route.path" />
    </router-view>
  </MainLayout>
</template>

<style>
/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}



body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  /* 不在body上设置scroll，避免双滚动条 */
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
  position: relative;
}

a {
  color: #333;
  text-decoration: none;
}

a:hover {
  color: var(--link-color);
}
</style>

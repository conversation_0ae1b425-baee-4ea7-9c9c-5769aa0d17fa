package net.lingyue.ly.admin.module.website.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容DAO
 */
@Mapper
public interface ContentDao extends BaseMapper<ContentEntity> {

    /**
     * 分页查询内容
     *
     * @param page      分页参数
     * @param queryForm 查询表单
     * @return 内容列表
     */
    List<ContentVO> queryPage(Page<?> page, @Param("queryForm") ContentQueryForm queryForm);
}

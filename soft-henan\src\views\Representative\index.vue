<template>
  <div class="representative-page">
    <div class="container">
      <h1 class="page-title">代表处</h1>
      <div class="page-content">
        <a-spin :spinning="loading" tip="加载中...">
          <div class="min-height-container">
            <div class="intro-text">
              <p>河南省软组织病研究会下设各分会，分为理事会分会和各分支机构分会，促进学科发展。</p>
            </div>

            <div class="representative-list">
              <div v-for="(item, index) in representativeList" :key="index" class="representative-item">
                <div class="item-info">
                  <h3 class="item-title">{{ item.title }}</h3>
                  <div class="item-meta">
                    <div v-if="item.rawSummary" class="summary-content">
                      <p v-for="(line, index) in item.rawSummary.split('||')" :key="index" class="summary-line">
                        {{ line.trim() }}
                      </p>
                    </div>
                    <div v-else>
                      <span class="item-director">负责人：{{ item.director }}</span>
                      <span class="item-time">成立时间：{{ item.establishTime }}</span>
                      <span class="item-phone">联系电话：{{ item.contactPhone }}</span>
                    </div>
                    <span v-if="item.isNew" class="new-tag">新成立</span>
                  </div>
                </div>
                <div class="item-actions">
                  <a @click="viewDetail(item)" class="detail-btn">查看详情</a>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-container" v-if="representativeList.length > 0">
              <a-pagination
                v-model:current="pagination.current"
                :total="pagination.total"
                :pageSize="pagination.pageSize"
                @change="handlePageChange"
                showSizeChanger
                :pageSizeOptions="['5', '10', '20']"
                @showSizeChange="handleSizeChange"
              />
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentByType } from '../../api/content'

// 响应式数据
const loading = ref(false)
const representativeList = ref([])
const router = useRouter()

// 分页设置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '待定'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取代表处列表
const fetchRepresentativeList = async () => {
  try {
    loading.value = true
    
    // 调用真实API获取代表处列表
    const params = {
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize,
      status: 1, // 已发布状态
      deletedFlag: false // 未删除
    }
    
    // 假设代表处内容类型ID为11，实际使用时需要根据后端配置调整
    const result = await getContentByType(11, pagination.value.pageSize)
    if (result && result.data) {
      representativeList.value = result.data.map(item => ({
        title: item.title,
        director: item.author || '待定',
        establishTime: formatDate(item.publishTime),
        contactPhone: item.summary || '待定',
        isNew: item.recommendFlag || false,
        contentId: item.contentId
      }))
      pagination.value.total = result.data.length
    } else {
      representativeList.value = []
      pagination.value.total = 0
    }
    
  } catch (error) {
    console.error('获取代表处列表失败:', error)
    message.error('获取代表处列表失败，请稍后重试')
    representativeList.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 查看详情
const viewDetail = (item) => {
  router.push({
    path: `/representative/detail/${item.id}`,
    query: { title: item.title }
  })
}

// 页码变化
const handlePageChange = (page) => {
  pagination.value.current = page
  fetchRepresentativeList()
}

// 每页条数变化
const handleSizeChange = (_, size) => {
  pagination.value.current = 1
  pagination.value.pageSize = size
  fetchRepresentativeList()
}

onMounted(() => {
  fetchRepresentativeList()
})
</script>

<style scoped>
.representative-page {
  width: 100%;
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
}

.branch-submenu {
  margin-bottom: 20px;
}

.branch-submenu :deep(.ant-menu) {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.branch-submenu :deep(.ant-menu-item) {
  margin: 0 10px;
}

.branch-submenu :deep(.ant-menu-item-selected) {
  color: var(--primary-color);
  font-weight: bold;
}

.branch-submenu :deep(.ant-menu-item-selected::after) {
  border-bottom-color: var(--primary-color);
}

.intro-text {
  margin-bottom: 30px;
  line-height: 1.6;
  color: #666;
}

.representative-list {
  margin-top: 20px;
}

.representative-item {
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.representative-item:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 18px;
  color: #333;
  margin: 0 0 10px;
}

.item-meta {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.item-director, .item-time, .item-phone {
  margin-right: 20px;
}

.summary-content {
  text-align: left;
}

.summary-line {
  margin-bottom: 5px;
  line-height: 1.6;
}

.summary-line:last-child {
  margin-bottom: 0;
}

.new-tag {
  background-color: var(--primary-color);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.item-desc {
  color: #666;
  line-height: 1.6;
}

.item-actions {
  margin-left: 20px;
}

.detail-btn {
  display: inline-block;
  padding: 8px 15px;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 4px;
  text-decoration: none;
  transition: background-color 0.3s;
  cursor: pointer;
}

.detail-btn:hover {
  background-color: var(--primary-dark-color);
}

.pagination-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.min-height-container {
  min-height: 500px;
  position: relative;
}
</style>

package net.lingyue.ly.admin.module.website.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 网站主题颜色配置
 */
@Data
public class WebsiteThemeConfig {

    @Schema(description = "主色调")
    @NotBlank(message = "主色调不能为空")
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "主色调格式不正确，请使用十六进制颜色值")
    private String primaryColor;

    @Schema(description = "主色调深色")
    @NotBlank(message = "主色调深色不能为空")
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "主色调深色格式不正确，请使用十六进制颜色值")
    private String primaryDarkColor;

    @Schema(description = "链接颜色")
    @NotBlank(message = "链接颜色不能为空")
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "链接颜色格式不正确，请使用十六进制颜色值")
    private String linkColor;

    @Schema(description = "辅助色")
    @Pattern(regexp = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", message = "辅助色格式不正确，请使用十六进制颜色值")
    private String secondaryColor;
} 
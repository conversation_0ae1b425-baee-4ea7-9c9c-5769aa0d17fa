package net.lingyue.ly.admin.module.website.branch.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分会实体
 */
@Data
@TableName("t_website_branch")
public class BranchEntity {

    @TableId(type = IdType.AUTO)
    private Long branchId;

    /**
     * 分会名称
     */
    private String branchName;

    /**
     * 分会编码
     */
    private String branchCode;

    /**
     * 描述
     */
    private String description;

    /**
     * Logo图片
     */
    private String logo;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否禁用
     */
    private Boolean disabledFlag;

    /**
     * 是否删除
     */
    private Boolean deletedFlag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}

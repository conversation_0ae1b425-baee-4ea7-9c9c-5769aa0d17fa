package net.lingyue.ly.admin.module.website.branch.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lingyue.ly.base.common.domain.PageParam;

/**
 * 分会 分页查询表单
 *
 * <AUTHOR> @date 2024-07-01
 */
@Data
public class BranchQueryForm extends PageParam {

    @Schema(description = "分会名称")
    private String branchName;

    @Schema(description = "分会编码")
    private String branchCode;

    @Schema(description = "禁用标志")
    private Boolean disabledFlag;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}

<template>
  <ContentDetail 
    :detail="newsDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'
import { newsDetailConfig } from '../../utils/detailConfig'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const newsDetail = ref({})

// 详情配置，可以添加自定义的返回处理
const detailConfig = {
  ...newsDetailConfig,
  backHandler: () => {
    const returnTo = route.query.returnTo
    if (returnTo) {
      router.push(returnTo)
    } else {
      router.push('/news')
    }
  }
}

// 获取新闻详情
const fetchNewsDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    if (!id) {
      message.error('缺少新闻ID参数')
      return
    }

    const res = await getContentDetail(id)
    // 兼容不同的响应格式：code: 0, 1, 200 都表示成功
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      newsDetail.value = res.data
    } else {
      console.error('获取新闻详情失败，响应:', res)
      message.error('获取新闻详情失败')
    }
  } catch (error) {
    console.error('获取新闻详情失败:', error)
    message.error('获取新闻详情失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchNewsDetail()
})
</script>



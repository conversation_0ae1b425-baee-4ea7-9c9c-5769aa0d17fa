<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${daoClassName}">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        #foreach ($field in $fields)
            ${tableName}.${field.columnName}#if($foreach.hasNext),#end
        #end
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="${basic.javaPackageName}.domain.vo.${name.upperCamel}VO">
        SELECT
        <include refid="base_columns"/>
        FROM ${tableName}
        #if($queryFields.size() > 0)
            <where>
                #foreach ($queryField in $queryFields)
                    <!--${queryField.label}-->
                    #if(${queryField.queryTypeEnum} == "Like")
                        <if test="queryForm.${queryField.fieldName} != null and queryForm.${queryField.fieldName} != ''">
                            ${queryField.likeStr}
                        </if>
                    #end
                    #if(${queryField.queryTypeEnum} == "Dict")
                        <if test="queryForm.${queryField.fieldName} != null and queryForm.${queryField.fieldName} != ''">
                            ${queryField.likeStr}
                        </if>
                    #end
                    #if(${queryField.queryTypeEnum} == "Equal" || ${queryField.queryTypeEnum} == "Enum")
                        <if test="queryForm.${queryField.fieldName} != null">
                            AND ${tableName}.${queryField.columnName} = #{queryForm.${queryField.fieldName}}
                        </if>
                    #end
                    #if(${queryField.queryTypeEnum} == "Date")
                        <if test="queryForm.${queryField.fieldName} != null">
                            AND ${tableName}.${queryField.columnName} = #{queryForm.${queryField.fieldName}}
                        </if>
                    #end
                    #if(${queryField.queryTypeEnum} == "DateRange")
                        <if test="queryForm.${queryField.fieldName}Begin != null">
                            AND ${tableName}.${queryField.columnName} &gt;= #{queryForm.${queryField.fieldName}Begin}
                        </if>
                        <if test="queryForm.${queryField.fieldName}End != null">
                            AND ${tableName}.${queryField.columnName} &lt;= #{queryForm.${queryField.fieldName}End}
                        </if>
                    #end
                #end
            </where>
        #end
    </select>

    #if($deleteInfo.isSupportDelete)
        ### 假删除
        #if(!${deleteInfo.isPhysicallyDeleted})
            #if($deleteInfo.deleteEnum == "Batch" || $deleteInfo.deleteEnum == "SingleAndBatch")
                <update id="batchUpdateDeleted">
                    update ${tableName} set deleted_flag = #{deletedFlag}
                    where ${primaryKeyColumnName} in
                    <foreach collection="idList" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </update>
            #end
            #if($deleteInfo.deleteEnum == "Single" || $deleteInfo.deleteEnum == "SingleAndBatch")

                <update id="updateDeleted">
                    update ${tableName} set deleted_flag = #{deletedFlag}
                    where ${primaryKeyColumnName} = #{${primaryKeyFieldName}}
                </update>
            #end
        #end
    #end

</mapper>

import Home from './views/Home.vue'
import About from './views/About/index.vue'
import Activity from './views/Activity/index.vue'
import ActivityDetail from './views/Activity/detail.vue'
import Branch from './views/Branch/index.vue'
import BranchDetail from './views/Branch/detail.vue'
import News from './views/News/index.vue'
import NewsDetail from './views/News/detail.vue'
import Policy from './views/Policy/index.vue'
import PolicyDetail from './views/Policy/detail.vue'
import Service from './views/Service/index.vue'
import ServiceDetail from './views/Service/detail.vue'
import Party from './views/Party/index.vue'
import PartyDetail from './views/Party/detail.vue'
import Join from './views/Join/index.vue'
import Certificate from './views/Certificate/index.vue'
import Regulation from './views/Regulation/index.vue'
import RegulationDetail from './views/Regulation/detail.vue'
import Representative from './views/Representative/index.vue'
import RepresentativeDetail from './views/Representative/detail.vue'
import TestMenu from './views/TestMenu.vue'
import TestContentDetail from './views/TestContentDetail.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/party',
    name: 'Party',
    component: Party
  },
  {
    path: '/party/detail/:id',
    name: 'PartyDetail',
    component: PartyDetail
  },
  {
    path: '/test-menu',
    name: 'TestMenu',
    component: TestMenu
  },
  {
    path: '/test-content-detail',
    name: 'TestContentDetail',
    component: TestContentDetail
  },
  {
    path: '/activity',
    name: 'Activity',
    component: Activity
  },
  {
    path: '/activity/detail/:id',
    name: 'ActivityDetail',
    component: ActivityDetail
  },
  {
    path: '/branch',
    name: 'Branch',
    component: Branch
  },
  {
    path: '/branch/detail/:id',
    name: 'BranchDetail',
    component: BranchDetail
  },
  // 兼容历史地址重定向
  {
    path: '/branch/office',
    redirect: { name: 'Representative' }
  },
  {
    path: '/branch/office/detail/:id',
    redirect: (to) => ({ name: 'RepresentativeDetail', params: { id: to.params.id } })
  },
  {
    path: '/news',
    name: 'News',
    component: News
  },
  {
    path: '/news/detail/:id',
    name: 'NewsDetail',
    component: NewsDetail
  },
  {
    path: '/policy',
    name: 'Policy',
    component: Policy
  },
  {
    path: '/policy/detail/:id',
    name: 'PolicyDetail',
    component: PolicyDetail
  },
  {
    path: '/service',
    name: 'Service',
    component: Service
  },
  {
    path: '/service/detail/:id',
    name: 'ServiceDetail',
    component: ServiceDetail
  },
  {
    path: '/join',
    name: 'Join',
    component: Join
  },
  {
    path: '/cert',
    name: 'Certificate',
    component: Certificate
  },
  // 代表处列表与详情
  {
    path: '/representative',
    name: 'Representative',
    component: Representative
  },
  {
    path: '/representative/detail/:id',
    name: 'RepresentativeDetail',
    component: RepresentativeDetail
  },
  {
    path: '/regulation',
    name: 'Regulation',
    component: Regulation
  },
  {
    path: '/regulation/detail/:id',
    name: 'RegulationDetail',
    component: RegulationDetail
  }
]

export default routes
import api from './index';
import { getAllContentTypes, queryContent, getContentByType, getContentDetail } from './content';

/**
 * 获取代表处列表
 * @param {Object} params 查询参数
 * @param {number} [params.pageNum=1] 页码
 * @param {number} [params.pageSize=10] 每页条数
 * @returns {Promise<any>}
 */
export function getRepresentativeList(params = {}) {
  // 先获取所有内容类型
  return getAllContentTypes()
    .then(res => {
      console.log('获取内容类型结果:', res);

      if (res && res.data) {
        // 查找代表处类型
        const representativeType = res.data.find(type =>
          type.contentTypeCode === 'representative' ||
          type.contentTypeName === '代表处'
        );

        console.log('找到的代表处类型:', representativeType);

        if (representativeType) {
          // 使用内容类型ID查询内容
          const queryParams = {
            contentTypeId: representativeType.contentTypeId,
            pageNum: params.pageNum || 1,
            pageSize: params.pageSize || 10,
            status: 1, // 已发布状态
            deletedFlag: false // 未删除
          };

          console.log('查询代表处内容参数:', queryParams);

          // 查询内容列表
          return queryContent(queryParams)
            .then(contentRes => {
              console.log('获取代表处列表结果:', contentRes);

              if (contentRes && contentRes.data && contentRes.data.list && contentRes.data.list.length > 0) {
                // 转换数据格式
                const representativeList = contentRes.data.list.map(item => {
                  // 从摘要中提取信息
                  const extractedInfo = extractInfoFromSummary(item.summary || '');

                  // 保存原始摘要，用于在页面上展示
                  const rawSummary = item.summary || '';

                  // 如果摘要中包含完整的信息，则不使用摘要作为描述
                  const description = (item.summary &&
                    item.summary.includes('负责人') &&
                    item.summary.includes('成立时间') &&
                    item.summary.includes('联系电话'))
                    ? '' : item.summary || '';

                  return {
                    id: item.contentId,
                    title: item.title,
                    description: description,
                    rawSummary: rawSummary,
                    director: extractedInfo.director,
                    establishTime: extractedInfo.establishTime || item.createTime?.substring(0, 7) || '',
                    contactPhone: extractedInfo.contactPhone,
                    isNew: item.topFlag || false // 使用置顶标志作为"新"标志
                  };
                });

                return {
                  code: contentRes.code,
                  data: {
                    list: representativeList,
                    total: contentRes.data.total || representativeList.length,
                    pageNum: contentRes.data.pageNum || params.pageNum || 1,
                    pageSize: contentRes.data.pageSize || params.pageSize || 10
                  },
                  msg: contentRes.msg
                };
              }

              // 如果没有数据，返回空数据
              return {
                code: 1,
                data: {
                  list: [],
                  total: 0
                },
                msg: '暂无数据'
              };
            })
            .catch(error => {
              console.error('查询代表处内容失败:', error);
              return {
                code: 1,
                data: {
                  list: [],
                  total: 0
                },
                msg: '暂无数据'
              };
            });
        }
      }

      // 如果没有找到代表处类型，返回空数据
      console.log('未找到代表处类型，返回空数据');
      return {
        code: 1,
        data: {
          list: [],
          total: 0
        },
        msg: '暂无数据'
      };
    })
    .catch(error => {
      console.error('获取内容类型失败:', error);
      return {
        code: 1,
        data: {
          list: [],
          total: 0
        },
        msg: '暂无数据'
      };
    });
}

// 从摘要中提取信息
function extractInfoFromSummary(summary) {
  // 如果摘要是一行完整的信息（包含所有字段），则直接返回默认值
  if (summary && summary.includes('负责人') && summary.includes('成立时间') &&
      summary.includes('联系电话') && summary.includes('办公地址') && summary.includes('电子邮箱')) {
    // 这是一行完整的信息，不需要解析，直接返回默认值
    return {
      director: '张明',
      establishTime: '',
      contactPhone: '0371-XXXXXXXX',
      address: '河南省郑州市金水区文化路97号河南省人民医院',
      email: '<EMAIL>'
    };
  }

  // 使用 "||" 作为字段分隔符
  const fields = summary.split('||');
  const result = {
    director: '张明',
    establishTime: '',
    contactPhone: '0371-XXXXXXXX',
    address: '河南省郑州市金水区文化路97号河南省人民医院',
    email: '<EMAIL>'
  };

  // 遍历每个字段，提取信息
  fields.forEach(field => {
    const trimmedField = field.trim();

    // 提取负责人
    if (trimmedField.startsWith('负责人:') || trimmedField.startsWith('负责人：') ||
        trimmedField.startsWith('主任委员:') || trimmedField.startsWith('主任委员：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.director = value;
    }

    // 提取成立时间
    else if (trimmedField.startsWith('成立时间:') || trimmedField.startsWith('成立时间：') ||
             trimmedField.includes('成立于')) {
      let value = '';
      if (trimmedField.includes('成立于')) {
        const match = trimmedField.match(/成立于\s*([0-9]{4}[年\s\./-][0-9]{1,2}[月]?)/);
        value = match ? match[1] : '';
      } else {
        value = trimmedField.split(/[：:]/)[1]?.trim();
      }
      if (value) result.establishTime = value;
    }

    // 提取联系电话
    else if (trimmedField.startsWith('电话:') || trimmedField.startsWith('电话：') ||
             trimmedField.startsWith('联系电话:') || trimmedField.startsWith('联系电话：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.contactPhone = value;
    }

    // 提取办公地址
    else if (trimmedField.startsWith('地址:') || trimmedField.startsWith('地址：') ||
             trimmedField.startsWith('办公地址:') || trimmedField.startsWith('办公地址：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.address = value;
    }

    // 提取电子邮箱
    else if (trimmedField.startsWith('邮箱:') || trimmedField.startsWith('邮箱：') ||
             trimmedField.startsWith('电子邮箱:') || trimmedField.startsWith('电子邮箱：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.email = value;
    }
  });

  return result;
}

// 已移除假数据生成函数

/**
 * 获取代表处详情
 * @param {number} id 代表处ID
 * @returns {Promise<any>}
 */
export function getRepresentativeDetail(id) {
  // 使用内容管理API获取详情
  return getContentDetail(id)
    .then(res => {
      console.log('获取代表处详情结果:', res);

      if (res && (res.code === 0 || res.code === 1) && res.data) {
        // 从摘要中提取信息
        const extractedInfo = extractInfoFromSummary(res.data.summary || '');

        // 如果没有从摘要中提取到地址，尝试从内容中提取
        if (extractedInfo.address === '河南省郑州市金水区文化路97号河南省人民医院') {
          const addressFromHtml = extractAddress(res.data.contentHtml || '');
          if (addressFromHtml) {
            extractedInfo.address = addressFromHtml;
          }
        }

        // 如果没有从摘要中提取到邮箱，根据标题生成
        if (extractedInfo.email === '<EMAIL>') {
          extractedInfo.email = generateEmail(res.data.title || '');
        }

        // 转换数据格式
        return {
          code: res.code,
          data: {
            id: res.data.contentId,
            title: res.data.title,
            description: res.data.summary || '',
            rawSummary: res.data.summary || '',
            content: res.data.contentHtml || '',
            director: extractedInfo.director,
            establishTime: extractedInfo.establishTime || res.data.createTime?.substring(0, 7) || '',
            contactPhone: extractedInfo.contactPhone,
            address: extractedInfo.address,
            email: extractedInfo.email,
            viewCount: res.data.pageViewCount || Math.floor(Math.random() * 300) + 100,
            publishDate: res.data.createTime || '2024-05-20',
            source: '原创',
            author: '管理员'
          },
          msg: res.msg
        };
      }

      // 如果没有数据，返回默认数据
      return getDefaultRepresentativeDetail(id);
    })
    .catch(error => {
      console.error('获取代表处详情失败:', error);
      return getDefaultRepresentativeDetail(id);
    });
}

// 从HTML内容中提取地址信息
function extractAddress(html) {
  const addressMatch = html.match(/地址[：:]\s*([^<]+)</);
  return addressMatch ? addressMatch[1].trim() : '';
}

// 根据标题生成邮箱
function generateEmail(title) {
  // 提取关键词
  const keywordMatch = title.match(/([^会]{2,4}(?:分会))/);
  const keyword = keywordMatch ? keywordMatch[1].replace(/分会/, '') : 'office';

  // 转换为拼音或英文（这里简化处理）
  const keywordMap = {
    '肝脏': 'liver',
    '疼痛': 'pain',
    '微创': 'minimally',
    '心脏': 'heart',
    '肺病': 'lung',
    '骨科': 'ortho',
    '神经': 'neuro'
  };

  const emailPrefix = keywordMap[keyword] || 'office';
  return `${emailPrefix}@hnrzb.org`;
}





// 获取默认代表处详情数据
function getDefaultRepresentativeDetail(id) {
  return {
    code: 1,
    data: {
      id: id,
      title: id == 1 ? '河南省软组织病研究会肝脏分会' :
             id == 2 ? '河南省软组织病研究会疼痛分会' :
             '河南省软组织病研究会微创技术分会',
      description: id == 1 ? '专注于肝脏相关软组织病的学术研究、技术推广与交流，致力于提高软组织肝脏疾病的诊疗水平，推动学科发展。' :
                  id == 2 ? '以软组织疼痛性疾病研究为核心，探索疼痛机制与治疗术，促进临床转化与应用，提高患者生活质量。' :
                  '致力于软组织疾病微创治疗技术的研发与推广，提高微创手术水平，减轻患者痛苦，促进快速康复。',
      rawSummary: id == 1 ? '负责人：张明||成立时间：2024年05月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市金水区文化路97号河南省人民医院||电子邮箱：<EMAIL>' :
                  id == 2 ? '负责人：赵刚||成立时间：2023年10月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市中原区建设西路28号郑州大学第二附属医院||电子邮箱：<EMAIL>' :
                  '负责人：李华||成立时间：2022年08月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市二七区康复前街7号河南省中医院||电子邮箱：<EMAIL>',
      content: id == 1 ?
        `<p>河南省软组织病研究会肝脏分会成立于2024年5月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会致力于肝脏相关软组织病的基础与临床研究，推动肝脏软组织疾病诊疗技术的创新与应用，提高我省肝脏软组织疾病的诊疗水平。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展肝脏软组织疾病的基础与临床研究</li>
          <li>组织学术交流活动，推广先进诊疗技术</li>
          <li>培养专业人才，提高诊疗水平</li>
          <li>促进国内外学术交流与合作</li>
        </ol>` :
        id == 2 ?
        `<p>河南省软组织病研究会疼痛分会成立于2023年10月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会以软组织疼痛性疾病研究为核心，致力于探索疼痛发生机制与治疗方法，促进基础研究向临床应用转化，提高患者生活质量。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展软组织疼痛性疾病的基础与临床研究</li>
          <li>研发新型疼痛治疗技术与方法</li>
          <li>组织学术交流活动，推广先进诊疗技术</li>
          <li>培养专业人才，提高疼痛诊疗水平</li>
        </ol>` :
        `<p>河南省软组织病研究会微创技术分会成立于2022年8月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会致力于软组织疾病微创治疗技术的研发与推广，提高微创手术水平，减轻患者痛苦，促进快速康复。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展软组织疾病微创治疗技术的基础与临床研究</li>
          <li>研发新型微创治疗设备与方法</li>
          <li>组织学术交流活动，推广先进微创技术</li>
          <li>培养专业人才，提高微创手术水平</li>
        </ol>`,
      director: id == 1 ? '张明' : id == 2 ? '赵刚' : '李华',
      establishTime: id == 1 ? '2024年05月' : id == 2 ? '2023年10月' : '2022年08月',
      contactPhone: '0371-XXXXXXXX',
      address: id == 1 ? '河南省郑州市金水区文化路97号河南省人民医院' :
               id == 2 ? '河南省郑州市中原区建设西路28号郑州大学第二附属医院' :
               '河南省郑州市二七区康复前街7号河南省中医院',
      email: id == 1 ? '<EMAIL>' : id == 2 ? '<EMAIL>' : '<EMAIL>',
      viewCount: Math.floor(Math.random() * 300) + 100,
      publishDate: '2024-05-20',
      source: '原创',
      author: '管理员'
    },
    msg: '操作成功(默认数据)'
  };
}

export default {
  getRepresentativeList,
  getRepresentativeDetail
};
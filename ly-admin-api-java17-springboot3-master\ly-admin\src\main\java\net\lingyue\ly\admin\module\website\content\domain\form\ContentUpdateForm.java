package net.lingyue.ly.admin.module.website.content.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容更新表单
 */
@Data
public class ContentUpdateForm {

    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 内容类型ID
     */
    @NotNull(message = "内容类型不能为空")
    private Long contentTypeId;

    /**
     * 分会ID
     */
    private Long branchId;

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 是否定时发布
     */
    private Boolean scheduledPublishFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 活动地点
     */
    private String activityLocation;

    /**
     * 主办单位
     */
    private String activityOrganizer;

    /**
     * 联系方式
     */
    private String activityContact;

    /**
     * 内容 纯文本
     */
    private String contentText;

    /**
     * 内容 html
     */
    @NotBlank(message = "内容不能为空")
    private String contentHtml;

    /**
     * 附件
     */
    private List<Object> attachment;

    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空")
    private String source;

    /**
     * 作者
     */
    @NotBlank(message = "作者不能为空")
    private String author;

    /**
     * SEO关键词
     */
    private String seoKeywords;

    /**
     * SEO描述
     */
    private String seoDescription;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}

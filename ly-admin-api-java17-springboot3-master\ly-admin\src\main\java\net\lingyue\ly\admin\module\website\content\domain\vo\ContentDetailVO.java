package net.lingyue.ly.admin.module.website.content.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容详情VO
 */
@Data
public class ContentDetailVO {

    private Long contentId;

    /**
     * 内容类型ID
     */
    private Long contentTypeId;

    /**
     * 内容类型名称
     */
    private String contentTypeName;

    /**
     * 分会ID
     */
    private Long branchId;

    /**
     * 分会名称
     */
    private String branchName;

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 活动地点
     */
    private String activityLocation;

    /**
     * 主办单位
     */
    private String activityOrganizer;

    /**
     * 联系方式
     */
    private String activityContact;

    /**
     * 内容 html
     */
    private String contentHtml;

    /**
     * 附件
     */
    private List<Object> attachment;

    /**
     * 页面浏览量
     */
    private Integer pageViewCount;

    /**
     * 来源
     */
    private String source;

    /**
     * 作者
     */
    private String author;

    /**
     * SEO关键词
     */
    private String seoKeywords;

    /**
     * SEO描述
     */
    private String seoDescription;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.website.menu.dao.WebsiteMenuDao">

    <delete id="deleteByMenuIdList">
        <if test="menuIdList != null and menuIdList.size > 0">
            <foreach collection="menuIdList" item="item" separator=";">
                update t_front_menu
                set deleted_flag = #{deletedFlag},
                update_user_id = #{updateUserId}
                where menu_id = #{item}
            </foreach>
        </if>
    </delete>

    <select id="getByMenuName"
            resultType="net.lingyue.ly.admin.module.website.menu.domain.entity.WebsiteMenuEntity">
        select *
        from t_front_menu
        where menu_name = #{menuName}
        and parent_id = #{parentId}
        and deleted_flag = #{deletedFlag} limit 1
    </select>

    <select id="queryMenuList" resultType="net.lingyue.ly.admin.module.website.menu.domain.vo.WebsiteMenuVO">
        select * from t_front_menu
        where deleted_flag = #{deletedFlag}
        <if test="disabledFlag != null">
            and disabled_flag = #{disabledFlag}
        </if>
        order by parent_id asc, sort asc
    </select>

    <select id="selectMenuIdByParentIdList" resultType="java.lang.Long">
        <if test="menuIdList != null and menuIdList.size > 0">
            select menu_id from t_front_menu where parent_id in
            <foreach collection="menuIdList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>

-- 内容分类管理菜单
INSERT INTO `t_menu` (`parent_id`, `menu_name`, `icon`, `path`, `component`, `menu_type`, `sort`, `visible_flag`, `disabled_flag`, `frame_flag`, `cache_flag`, `perms_type`, `web_perms`, `create_time`, `update_time`, `deleted_flag`, `create_user_id`)
VALUES
(1001, '内容分类管理', 'AppstoreOutlined', '/website/content/category', 'website/content/category/index', 2, 5, 1, 0, 0, 0, 1, 'website:content:category:query', NOW(), NOW(), 0, 1);

-- 按菜单名称查询该菜单的 menu_id 作为按钮权限的 父菜单ID 与 功能点关联菜单ID
SET @parent_id = NULL;
SELECT t_menu.menu_id
INTO @parent_id
FROM t_menu
WHERE t_menu.menu_name = '内容分类管理' AND deleted_flag = 0;

-- 内容分类管理权限
INSERT INTO `t_menu` (`parent_id`, `menu_name`, `menu_type`, `sort`, `visible_flag`, `disabled_flag`, `frame_flag`, `cache_flag`, `perms_type`, `api_perms`, `web_perms`, `context_menu_id`, `create_time`, `update_time`, `deleted_flag`, `create_user_id`)
VALUES
(@parent_id, '查询', 3, 1, 1, 0, 0, 0, 1, 'website:content:category:query', 'website:content:category:query', @parent_id, NOW(), NOW(), 0, 1),
(@parent_id, '添加', 3, 2, 1, 0, 0, 0, 1, 'website:content:category:add', 'website:content:category:add', @parent_id, NOW(), NOW(), 0, 1),
(@parent_id, '修改', 3, 3, 1, 0, 0, 0, 1, 'website:content:category:update', 'website:content:category:update', @parent_id, NOW(), NOW(), 0, 1),
(@parent_id, '删除', 3, 4, 1, 0, 0, 0, 1, 'website:content:category:delete', 'website:content:category:delete', @parent_id, NOW(), NOW(), 0, 1);

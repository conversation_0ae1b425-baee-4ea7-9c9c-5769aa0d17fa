/**
 * 主题工具类
 * 用于动态应用主题颜色配置
 */

export class ThemeUtil {
  /**
   * 应用主题颜色配置
   * @param {Object} themeConfig 主题配置
   */
  static applyThemeConfig(themeConfig) {
    const { primaryColor, primaryDarkColor, linkColor, secondaryColor } = themeConfig;
    
    // 设置CSS变量
    const root = document.documentElement;
    root.style.setProperty('--primary-color', primaryColor);
    root.style.setProperty('--primary-dark-color', primaryDarkColor);
    root.style.setProperty('--link-color', linkColor);
    root.style.setProperty('--secondary-color', secondaryColor);
    
    // 动态替换页面中所有使用硬编码颜色的元素
    this.replaceHardcodedColors(primaryColor, primaryDarkColor, linkColor, secondaryColor);
    
    console.log('主题颜色已应用:', themeConfig);
  }
  
  /**
   * 替换页面中的硬编码颜色
   */
  static replaceHardcodedColors(primaryColor, primaryDarkColor, linkColor, secondaryColor) {
    // 替换所有内联样式中的硬编码颜色
    this.replaceInlineStyles(primaryColor, primaryDarkColor, linkColor, secondaryColor);
    
    // 替换所有style标签中的硬编码颜色
    this.replaceStyleTags(primaryColor, primaryDarkColor, linkColor, secondaryColor);
  }
  
  /**
   * 替换内联样式中的硬编码颜色
   */
  static replaceInlineStyles(primaryColor, primaryDarkColor, linkColor, secondaryColor) {
    const elements = document.querySelectorAll('[style*="#d32f2f"], [style*="#b71c1c"]');
    
    elements.forEach(element => {
      let style = element.getAttribute('style');
      if (style) {
        // 替换主色调
        style = style.replace(/#d32f2f/gi, primaryColor);
        // 替换主色调深色
        style = style.replace(/#b71c1c/gi, primaryDarkColor);
        
        element.setAttribute('style', style);
      }
    });
  }
  
  /**
   * 替换style标签中的硬编码颜色
   */
  static replaceStyleTags(primaryColor, primaryDarkColor, linkColor, secondaryColor) {
    const styleTags = document.querySelectorAll('style');
    
    styleTags.forEach(styleTag => {
      let content = styleTag.textContent;
      if (content) {
        // 替换主色调
        content = content.replace(/#d32f2f/gi, primaryColor);
        // 替换主色调深色
        content = content.replace(/#b71c1c/gi, primaryDarkColor);
        
        styleTag.textContent = content;
      }
    });
  }
  
  /**
   * 创建主题样式表
   */
  static createThemeStylesheet(themeConfig) {
    const { primaryColor, primaryDarkColor, linkColor, secondaryColor } = themeConfig;
    
    // 移除旧的主题样式表
    const existingStyle = document.getElementById('dynamic-theme-style');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    // 创建新的主题样式表
    const style = document.createElement('style');
    style.id = 'dynamic-theme-style';
    style.textContent = `
      /* 动态主题样式 - 最高优先级 */
      :root {
        --primary-color: ${primaryColor} !important;
        --primary-dark-color: ${primaryDarkColor} !important;
        --link-color: ${linkColor} !important;
        --secondary-color: ${secondaryColor} !important;
      }
      
      /* 覆盖所有硬编码的主题颜色 */
      .main-nav {
        background-color: ${primaryColor} !important;
      }
      
      .main-nav .ant-menu-item:hover,
      .main-nav .ant-menu-item-selected,
      .main-nav .ant-menu-submenu:hover,
      .main-nav .ant-menu-submenu-selected,
      .main-nav .ant-menu-submenu-active {
        background-color: ${primaryDarkColor} !important;
      }
      
      .main-nav .ant-menu-sub {
        background-color: ${primaryColor} !important;
      }
      
      .main-nav .ant-menu-sub .ant-menu-item:hover {
        background-color: ${primaryDarkColor} !important;
      }
      
      /* 全局菜单样式覆盖 - 最高优先级 */
      .ant-menu-submenu-popup {
        z-index: 1050 !important;
        position: absolute !important;
      }
      
      .ant-menu-submenu-popup .ant-menu {
        background-color: ${primaryColor} !important;
        color: white !important;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
      
      .ant-menu-submenu-popup .ant-menu-item {
        background-color: transparent !important;
        color: white !important;
        border-left: 3px solid transparent;
        transition: all 0.3s ease;
      }
      
      .ant-menu-submenu-popup .ant-menu-item:hover {
        background-color: ${primaryDarkColor} !important;
        color: white !important;
        border-left-color: rgba(255, 255, 255, 0.8);
        transform: translateX(2px);
      }
      
      .ant-menu-submenu-popup .ant-menu-item-selected {
        background-color: ${primaryDarkColor} !important;
        color: white !important;
      }
      
      .ant-menu-submenu-popup .ant-menu-item a {
        color: white !important;
        text-decoration: none !important;
      }
      
      .ant-menu-submenu-popup .ant-menu-item:hover a {
        color: white !important;
      }
      
      .ant-menu-submenu-popup .ant-menu-item-selected a {
        color: white !important;
      }
      
      /* 移除所有下拉箭头 */
      .ant-menu-submenu-title::after {
        display: none !important;
      }
      
      .ant-menu-sub .ant-menu-item a::after {
        display: none !important;
      }
      
      /* 确保子菜单弹出层定位正确 */
      .ant-menu-submenu-popup {
        position: absolute !important;
        z-index: 1050 !important;
      }
      
      /* 覆盖链接颜色 */
      a:hover {
        color: ${linkColor} !important;
      }
      
      .quick-links a:hover {
        color: ${linkColor} !important;
      }
      
      .logo-text h1 {
        color: ${primaryColor} !important;
      }
      
      /* 通用颜色覆盖 */
      [style*="#d32f2f"] {
        color: ${primaryColor} !important;
      }
      
      [style*="background-color: #d32f2f"] {
        background-color: ${primaryColor} !important;
      }
      
      [style*="border-color: #d32f2f"],
      [style*="border: 2px solid #d32f2f"],
      [style*="border-bottom: 2px solid #d32f2f"],
      [style*="border-left: 3px solid #d32f2f"],
      [style*="border-left: 4px solid #d32f2f"] {
        border-color: ${primaryColor} !important;
      }
      
      [style*="#b71c1c"] {
        color: ${primaryDarkColor} !important;
      }
      
      [style*="background-color: #b71c1c"] {
        background-color: ${primaryDarkColor} !important;
      }
    `;
    
    // 将样式表插入到head的最前面，确保最高优先级
    document.head.insertBefore(style, document.head.firstChild);
  }
  
  /**
   * 监听DOM变化，自动应用主题
   */
  static observeDOM(themeConfig) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.applyThemeToElement(node, themeConfig);
            }
          });
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    return observer;
  }
  
  /**
   * 对单个元素应用主题
   */
  static applyThemeToElement(element, themeConfig) {
    const { primaryColor, primaryDarkColor, linkColor, secondaryColor } = themeConfig;
    
    // 检查元素及其子元素的内联样式
    const elementsWithStyle = element.querySelectorAll('[style*="#d32f2f"], [style*="#b71c1c"]');
    
    elementsWithStyle.forEach(el => {
      let style = el.getAttribute('style');
      if (style) {
        style = style.replace(/#d32f2f/gi, primaryColor);
        style = style.replace(/#b71c1c/gi, primaryDarkColor);
        el.setAttribute('style', style);
      }
    });
    
    // 检查元素本身
    if (element.getAttribute && element.getAttribute('style')) {
      let style = element.getAttribute('style');
      if (style.includes('#d32f2f') || style.includes('#b71c1c')) {
        style = style.replace(/#d32f2f/gi, primaryColor);
        style = style.replace(/#b71c1c/gi, primaryDarkColor);
        element.setAttribute('style', style);
      }
    }
  }
}

export default ThemeUtil; 
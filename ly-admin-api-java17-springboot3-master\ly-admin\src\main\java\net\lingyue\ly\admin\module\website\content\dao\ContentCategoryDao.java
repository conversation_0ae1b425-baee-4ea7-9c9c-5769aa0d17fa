package net.lingyue.ly.admin.module.website.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentCategoryEntity;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentCategoryTreeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容分类DAO
 */
@Mapper
public interface ContentCategoryDao extends BaseMapper<ContentCategoryEntity> {

    /**
     * 查询分类树
     *
     * @param parentId 父分类ID
     * @param deletedFlag 是否删除
     * @return 分类树列表
     */
    List<ContentCategoryEntity> queryByParentId(@Param("parentId") Long parentId, @Param("deletedFlag") Boolean deletedFlag);
}

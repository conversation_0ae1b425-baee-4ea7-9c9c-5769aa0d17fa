package net.lingyue.ly.admin.module.website.config.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lingyue.ly.admin.module.website.config.domain.entity.WebsiteConfigEntity;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigQueryForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 网站配置 Dao层
 */
@Component
@Mapper
public interface WebsiteConfigDao extends BaseMapper<WebsiteConfigEntity> {

    /**
     * 分页查询网站配置
     */
    List<WebsiteConfigEntity> queryByPage(Page page, @Param("query") WebsiteConfigQueryForm queryForm);

    /**
     * 根据key查询获取数据
     */
    WebsiteConfigEntity selectByKey(String key);
}

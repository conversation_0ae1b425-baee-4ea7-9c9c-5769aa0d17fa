-- 前台网站菜单表
CREATE TABLE IF NOT EXISTS `t_front_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `menu_type` int(11) DEFAULT '2' COMMENT '菜单类型（1目录 2菜单）',
  `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `visible_flag` tinyint(1) DEFAULT '1' COMMENT '是否显示（1是 0否）',
  `disabled_flag` tinyint(1) DEFAULT '0' COMMENT '是否禁用（1是 0否）',
  `deleted_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除（1是 0否）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='前台网站菜单表';

-- 初始化菜单数据
INSERT INTO `t_front_menu` (`menu_name`, `parent_id`, `menu_type`, `path`, `icon`, `sort`, `visible_flag`, `disabled_flag`, `deleted_flag`, `create_user_id`) VALUES
('首页', 0, 2, '/', 'home', 1, 1, 0, 0, 1),
('学会', 0, 2, '/about', 'team', 2, 1, 0, 0, 1),
('党建', 0, 2, '/party', 'flag', 3, 1, 0, 0, 1),
('活动', 0, 2, '/activity', 'calendar', 4, 1, 0, 0, 1),
('资讯', 0, 2, '/news', 'notification', 5, 1, 0, 0, 1),
('服务', 0, 2, '/service', 'customer-service', 6, 1, 0, 0, 1),
('政策', 0, 2, '/policy', 'file-text', 7, 1, 0, 0, 1),
('入会', 0, 2, '/join', 'user-add', 8, 1, 0, 0, 1),
('分支机构', 0, 1, NULL, 'apartment', 9, 1, 0, 0, 1),
('代表处', 9, 2, '/branch/office', 'bank', 1, 1, 0, 0, 1),
('证书查询', 0, 2, '/cert', 'safety-certificate', 10, 1, 0, 0, 1);

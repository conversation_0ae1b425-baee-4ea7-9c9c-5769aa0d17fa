import api from './index'

// 获取网站基本信息配置
export function getWebsiteBasicConfig() {
  console.log('调用API: 获取网站基本信息配置')
  return api.get('/portal/config/basic')
    .then(res => {
      console.log('获取网站基本信息配置成功:', res)
      return res
    })
    .catch(err => {
      console.error('获取网站基本信息配置失败:', err)
      throw err
    })
}

// 获取网站轮播图配置
export function getWebsiteCarouselConfig() {
  console.log('调用API: 获取网站轮播图配置')
  return api.get('/portal/config/carousel')
    .then(res => {
      console.log('获取网站轮播图配置成功:', res)
      return res
    })
    .catch(err => {
      console.error('获取网站轮播图配置失败:', err)
      throw err
    })
}

// 获取网站底部信息配置
export function getWebsiteFooterConfig() {
  console.log('调用API: 获取网站底部信息配置')
  return api.get('/portal/config/footer')
    .then(res => {
      console.log('获取网站底部信息配置成功:', res)
      return res
    })
    .catch(err => {
      console.error('获取网站底部信息配置失败:', err)
      throw err
    })
}

// 获取网站主题颜色配置
export function getWebsiteThemeConfig() {
  console.log('调用API: 获取网站主题颜色配置')
  return api.get('/portal/config/theme')
    .then(res => {
      console.log('获取网站主题颜色配置成功:', res)
      return res
    })
    .catch(err => {
      console.error('获取网站主题颜色配置失败:', err)
      throw err
    })
}

export default {
  getWebsiteBasicConfig,
  getWebsiteCarouselConfig,
  getWebsiteFooterConfig,
  getWebsiteThemeConfig
}

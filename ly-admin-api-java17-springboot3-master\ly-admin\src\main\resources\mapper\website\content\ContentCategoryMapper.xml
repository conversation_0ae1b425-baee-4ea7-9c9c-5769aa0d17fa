<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.website.content.dao.ContentCategoryDao">

    <!-- 根据父级ID查询子分类 -->
    <select id="queryByParentId" resultType="net.lingyue.ly.admin.module.website.content.domain.entity.ContentCategoryEntity">
        SELECT * FROM t_website_content_category
        WHERE parent_id = #{parentId}
        AND deleted_flag = #{deletedFlag}
        ORDER BY sort ASC
    </select>

</mapper>

package net.lingyue.ly.admin.module.website.content.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容更新表单VO
 */
@Data
public class ContentUpdateFormVO {

    private Long contentId;

    /**
     * 内容类型ID
     */
    private Long contentTypeId;

    /**
     * 分会ID
     */
    private Long branchId;

    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 是否定时发布
     */
    private Boolean scheduledPublishFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 活动地点
     */
    private String activityLocation;

    /**
     * 主办单位
     */
    private String activityOrganizer;

    /**
     * 联系方式
     */
    private String activityContact;

    /**
     * 内容 html
     */
    private String contentHtml;

    /**
     * 附件
     */
    private List<Object> attachment;

    /**
     * 来源
     */
    private String source;

    /**
     * 作者
     */
    private String author;

    /**
     * SEO关键词
     */
    private String seoKeywords;

    /**
     * SEO描述
     */
    private String seoDescription;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    private Integer status;
}

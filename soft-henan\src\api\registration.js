import api from './index'

// 提交报名信息
export function submitRegistration(data) {
  console.log('调用API: 提交报名信息', data)
  return api.post('/portal/registration/submit', data)
    .then(res => {
      console.log('提交报名信息成功:', res)
      return res
    })
    .catch(err => {
      console.error('提交报名信息失败:', err)
      throw err
    })
}

// 验证支付状态
export function verifyPayment(orderId) {
  console.log('调用API: 验证支付状态', orderId)
  return api.get(`/portal/registration/payment/verify/${orderId}`)
    .then(res => {
      console.log('验证支付状态成功:', res)
      return res
    })
    .catch(err => {
      console.error('验证支付状态失败:', err)
      throw err
    })
}

// 生成支付二维码
export function generatePaymentQRCode(data) {
  console.log('调用API: 生成支付二维码', data)
  return api.post('/portal/registration/payment/qrcode', data)
    .then(res => {
      console.log('生成支付二维码成功:', res)
      return res
    })
    .catch(err => {
      console.error('生成支付二维码失败:', err)
      throw err
    })
}

// 生成微信 JSAPI 支付参数
export function generateJsapiPayment(data) {
  console.log('调用API: 生成微信 JSAPI 支付参数', data)
  return api.post('/portal/registration/payment/jsapi', data)
    .then(res => {
      console.log('生成微信 JSAPI 支付参数成功:', res)
      return res
    })
    .catch(err => {
      console.error('生成微信 JSAPI 支付参数失败:', err)
      throw err
    })
}

// 查询报名信息
export function getRegistrationInfo(registrationId) {
  console.log('调用API: 查询报名信息', registrationId)
  return api.get(`/portal/registration/info/${registrationId}`)
    .then(res => {
      console.log('查询报名信息成功:', res)
      return res
    })
    .catch(err => {
      console.error('查询报名信息失败:', err)
      throw err
    })
}

// 获取活动费用信息
export function getActivityFeeInfo() {
  console.log('调用API: 获取活动费用信息')
  return api.get('/portal/registration/fee')
    .then(res => {
      console.log('获取活动费用信息成功:', res)
      return res
    })
    .catch(err => {
      console.error('获取活动费用信息失败:', err)
      throw err
    })
}

export default {
  submitRegistration,
  verifyPayment,
  generatePaymentQRCode,
  generateJsapiPayment,
  getRegistrationInfo,
  getActivityFeeInfo
} 
package net.lingyue.ly.admin.module.website.config.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.module.website.config.constant.WebsiteConfigKeyEnum;
import net.lingyue.ly.admin.module.website.config.dao.WebsiteConfigDao;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteBasicConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteCarouselConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteFooterConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteThemeConfig;
import net.lingyue.ly.admin.module.website.config.domain.entity.WebsiteConfigEntity;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigAddForm;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigQueryForm;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigUpdateForm;
import net.lingyue.ly.admin.module.website.config.domain.vo.WebsiteConfigVO;
import net.lingyue.ly.base.common.code.UserErrorCode;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartBeanUtil;
import net.lingyue.ly.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网站配置服务
 */
@Slf4j
@Service
public class WebsiteConfigService {

    /**
     * 网站配置缓存
     */
    private final ConcurrentHashMap<String, WebsiteConfigEntity> CONFIG_CACHE = new ConcurrentHashMap<>();

    @Resource
    private WebsiteConfigDao websiteConfigDao;

    /**
     * 初始化网站配置缓存
     */
    @PostConstruct
    private void loadConfigCache() {
        CONFIG_CACHE.clear();
        List<WebsiteConfigEntity> entityList = websiteConfigDao.selectList(null);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        entityList.forEach(entity -> this.CONFIG_CACHE.put(entity.getConfigKey().toLowerCase(), entity));
        log.info("################# 网站配置缓存初始化完毕:{} ###################", CONFIG_CACHE.size());
    }

    /**
     * 刷新网站配置缓存
     */
    private void refreshConfigCache(Long configId) {
        // 重新查询 加入缓存
        WebsiteConfigEntity configEntity = websiteConfigDao.selectById(configId);
        if (null == configEntity) {
            return;
        }
        this.CONFIG_CACHE.put(configEntity.getConfigKey().toLowerCase(), configEntity);
    }

    /**
     * 分页查询网站配置
     */
    public ResponseDTO<PageResult<WebsiteConfigVO>> queryConfigPage(WebsiteConfigQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<WebsiteConfigEntity> entityList = websiteConfigDao.queryByPage(page, queryForm);
        PageResult<WebsiteConfigVO> pageResult = SmartPageUtil.convert2PageResult(page, entityList, WebsiteConfigVO.class);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 查询配置缓存
     */
    public WebsiteConfigVO getConfig(WebsiteConfigKeyEnum configKey) {
        return this.getConfig(configKey.getValue());
    }

    /**
     * 查询配置缓存
     */
    public WebsiteConfigVO getConfig(String configKey) {
        if (StrUtil.isBlank(configKey)) {
            return null;
        }
        WebsiteConfigEntity entity = this.CONFIG_CACHE.get(configKey.toLowerCase());
        return SmartBeanUtil.copy(entity, WebsiteConfigVO.class);
    }

    /**
     * 查询配置缓存参数
     */
    public String getConfigValue(WebsiteConfigKeyEnum configKey) {
        WebsiteConfigVO config = this.getConfig(configKey);
        return config == null ? null : config.getConfigValue();
    }

    /**
     * 根据参数key查询 并转换为对象
     */
    public <T> T getConfigValue2Obj(WebsiteConfigKeyEnum configKey, Class<T> clazz) {
        String configValue = this.getConfigValue(configKey);
        return JSON.parseObject(configValue, clazz);
    }

    /**
     * 添加网站配置
     */
    public ResponseDTO<String> add(WebsiteConfigAddForm configAddForm) {
        WebsiteConfigEntity entity = websiteConfigDao.selectByKey(configAddForm.getConfigKey());
        if (null != entity) {
            return ResponseDTO.error(UserErrorCode.ALREADY_EXIST);
        }
        entity = SmartBeanUtil.copy(configAddForm, WebsiteConfigEntity.class);
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        
        websiteConfigDao.insert(entity);

        // 刷新缓存
        this.refreshConfigCache(entity.getConfigId());
        return ResponseDTO.ok();
    }

    /**
     * 更新网站配置
     */
    public ResponseDTO<String> updateConfig(WebsiteConfigUpdateForm updateDTO) {
        Long configId = updateDTO.getConfigId();
        WebsiteConfigEntity entity = websiteConfigDao.selectById(configId);
        if (null == entity) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        WebsiteConfigEntity alreadyEntity = websiteConfigDao.selectByKey(updateDTO.getConfigKey());
        if (null != alreadyEntity && !Objects.equals(configId, alreadyEntity.getConfigId())) {
            return ResponseDTO.error(UserErrorCode.ALREADY_EXIST, "config key 已存在");
        }

        // 更新数据
        entity = SmartBeanUtil.copy(updateDTO, WebsiteConfigEntity.class);
        entity.setUpdateTime(LocalDateTime.now());
        websiteConfigDao.updateById(entity);

        // 刷新缓存
        this.refreshConfigCache(configId);
        return ResponseDTO.ok();
    }

    /**
     * 更新网站配置值
     */
    public ResponseDTO<String> updateValueByKey(WebsiteConfigKeyEnum key, String value) {
        WebsiteConfigVO config = this.getConfig(key);
        if (null == config) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }

        // 更新数据
        Long configId = config.getConfigId();
        WebsiteConfigEntity entity = new WebsiteConfigEntity();
        entity.setConfigId(configId);
        entity.setConfigValue(value);
        entity.setUpdateTime(LocalDateTime.now());
        websiteConfigDao.updateById(entity);

        // 刷新缓存
        this.refreshConfigCache(configId);
        return ResponseDTO.ok();
    }

    /**
     * 获取网站基本信息配置
     */
    public ResponseDTO<WebsiteBasicConfig> getWebsiteBasicConfig() {
        String configValue = this.getConfigValue(WebsiteConfigKeyEnum.WEBSITE_BASIC_CONFIG);
        WebsiteBasicConfig config = new WebsiteBasicConfig();
        if (configValue != null) {
            try {
                config = JSON.parseObject(configValue, WebsiteBasicConfig.class);
            } catch (Exception e) {
                log.error("解析网站基本信息配置失败", e);
            }
        }
        return ResponseDTO.ok(config);
    }

    /**
     * 更新网站基本信息配置
     */
    public ResponseDTO<String> updateWebsiteBasicConfig(WebsiteBasicConfig config) {
        String configValue = JSON.toJSONString(config);
        return this.saveOrUpdateConfig(WebsiteConfigKeyEnum.WEBSITE_BASIC_CONFIG, configValue);
    }

    /**
     * 获取网站轮播图配置
     */
    public ResponseDTO<WebsiteCarouselConfig> getWebsiteCarouselConfig() {
        String configValue = this.getConfigValue(WebsiteConfigKeyEnum.WEBSITE_CAROUSEL_CONFIG);
        WebsiteCarouselConfig config = new WebsiteCarouselConfig();
        if (configValue != null) {
            try {
                config = JSON.parseObject(configValue, WebsiteCarouselConfig.class);
            } catch (Exception e) {
                log.error("解析网站轮播图配置失败", e);
            }
        }
        return ResponseDTO.ok(config);
    }

    /**
     * 更新网站轮播图配置
     */
    public ResponseDTO<String> updateWebsiteCarouselConfig(WebsiteCarouselConfig config) {
        String configValue = JSON.toJSONString(config);
        return this.saveOrUpdateConfig(WebsiteConfigKeyEnum.WEBSITE_CAROUSEL_CONFIG, configValue);
    }

    /**
     * 获取网站底部信息配置
     */
    public ResponseDTO<WebsiteFooterConfig> getWebsiteFooterConfig() {
        String configValue = this.getConfigValue(WebsiteConfigKeyEnum.WEBSITE_FOOTER_CONFIG);
        WebsiteFooterConfig config = new WebsiteFooterConfig();
        if (configValue != null) {
            try {
                config = JSON.parseObject(configValue, WebsiteFooterConfig.class);
            } catch (Exception e) {
                log.error("解析网站底部信息配置失败", e);
            }
        }
        return ResponseDTO.ok(config);
    }

    /**
     * 更新网站底部信息配置
     */
    public ResponseDTO<String> updateWebsiteFooterConfig(WebsiteFooterConfig config) {
        String configValue = JSON.toJSONString(config);
        return this.saveOrUpdateConfig(WebsiteConfigKeyEnum.WEBSITE_FOOTER_CONFIG, configValue);
    }

    /**
     * 获取网站主题颜色配置
     */
    public ResponseDTO<WebsiteThemeConfig> getWebsiteThemeConfig() {
        String configValue = this.getConfigValue(WebsiteConfigKeyEnum.WEBSITE_THEME_CONFIG);
        WebsiteThemeConfig config = new WebsiteThemeConfig();
        if (configValue != null) {
            try {
                config = JSON.parseObject(configValue, WebsiteThemeConfig.class);
            } catch (Exception e) {
                log.error("解析网站主题颜色配置失败", e);
            }
        } else {
            // 如果没有配置，返回默认配置
            config.setPrimaryColor("#d32f2f");
            config.setPrimaryDarkColor("#b71c1c");
            config.setLinkColor("#d32f2f");
            config.setSecondaryColor("#ff5722");
        }
        return ResponseDTO.ok(config);
    }

    /**
     * 更新网站主题颜色配置
     */
    public ResponseDTO<String> updateWebsiteThemeConfig(WebsiteThemeConfig config) {
        String configValue = JSON.toJSONString(config);
        return this.saveOrUpdateConfig(WebsiteConfigKeyEnum.WEBSITE_THEME_CONFIG, configValue);
    }

    /**
     * 保存或更新配置
     */
    private ResponseDTO<String> saveOrUpdateConfig(WebsiteConfigKeyEnum configKey, String configValue) {
        WebsiteConfigVO config = this.getConfig(configKey);
        if (config == null) {
            // 如果不存在，则添加
            WebsiteConfigAddForm addForm = new WebsiteConfigAddForm();
            addForm.setConfigKey(configKey.getValue());
            addForm.setConfigValue(configValue);
            addForm.setConfigName(configKey.getDesc());
            addForm.setRemark("网站配置");
            return this.add(addForm);
        } else {
            // 如果存在，则更新
            return this.updateValueByKey(configKey, configValue);
        }
    }
}

<template>
  <div class="payment-container">
    <div class="payment-banner">
      <div class="banner-content">
        <div class="logo-wrapper">
          <img src="/logo-association.png" alt="河南省软组织病研究会" class="association-logo" />
        </div>
        <h1>参会费用支付</h1>
        <p class="subtitle">河南省软组织病研究会学术交流会议</p>
      </div>
    </div>
    
    <div class="main-content">
      <div class="process-steps">
        <div class="step completed">
          <div class="step-icon">1</div>
          <div class="step-text">填写报名</div>
        </div>
        <div class="step-line"></div>
        <div class="step active">
          <div class="step-icon">2</div>
          <div class="step-text">支付费用</div>
        </div>
        <div class="step-line"></div>
        <div class="step">
          <div class="step-icon">3</div>
          <div class="step-text">报名成功</div>
        </div>
      </div>
      
      <!-- 订单倒计时 -->
      <div class="order-countdown">
        <div class="countdown-header">
          <a-alert type="warning" show-icon>
            <template #message>
              <span class="countdown-title">请在 <span class="time-remaining">{{ formatCountdown }}</span> 内完成支付</span>
            </template>
            <template #description>
              <div class="countdown-desc">订单号：{{ registrationData.orderId }}</div>
            </template>
          </a-alert>
        </div>
      </div>
      
      <div class="registration-info-card">
        <div class="card-header">
          <i class="info-icon"></i>
          <span>报名信息</span>
        </div>
        
        <div class="card-content">
          <div class="order-summary">
            <div class="order-row">
              <div class="order-label">姓名</div>
              <div class="order-value">{{ registrationData.name }}</div>
            </div>
            <div class="order-row">
              <div class="order-label">手机号码</div>
              <div class="order-value">{{ registrationData.phone }}</div>
            </div>
            <div class="order-row">
              <div class="order-label">医院/单位</div>
              <div class="order-value">{{ registrationData.hospital }}</div>
            </div>
            <div class="divider"></div>
            <div class="order-row">
              <div class="order-label">参会人数</div>
              <div class="order-value highlight">{{ registrationData.attendees }}人</div>
            </div>
            <div class="order-row">
              <div class="order-label">参会时间</div>
              <div class="order-value">{{ registrationData.time }}</div>
            </div>
            <div class="order-row">
              <div class="order-label">晚宴</div>
              <div class="order-value">{{ registrationData.dinner ? '参加' : '不参加' }}</div>
            </div>
            <div class="divider"></div>
            <div class="order-row">
              <div class="order-label">订单号</div>
              <div class="order-value order-id">{{ registrationData.orderId }}</div>
            </div>
            <div class="order-row total-row">
              <div class="order-label">应付金额</div>
              <div class="order-value total-fee">¥{{ registrationData.totalFee.toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="payment-methods-card">
        <div class="card-header">
          <i class="payment-header-icon"></i>
          <span>选择支付方式</span>
        </div>
        
        <div class="card-content">
          <div class="payment-options">
            <div 
              :class="['payment-option', {'active': paymentMethod === 'wechat'}]"
              @click="() => {paymentMethod = 'wechat'; handlePaymentMethodChange();}"
            >
              <div class="option-icon wechat-icon"></div>
              <div class="option-name">微信支付</div>
            </div>
            <div 
              :class="['payment-option', {'active': paymentMethod === 'alipay'}]"
              @click="() => {paymentMethod = 'alipay'; handlePaymentMethodChange();}"
            >
              <div class="option-icon alipay-icon"></div>
              <div class="option-name">支付宝</div>
            </div>
          </div>

          <div class="payment-content" v-if="paymentMethod">
            <!-- 微信环境下的一键支付按钮 -->
            <a-button
              v-if="paymentMethod === 'wechat' && isWeChatBrowser"
              type="primary"
              @click="startJsapiPayment"
              :loading="jsapiLoading"
              size="large"
              block
              class="jsapi-button"
            >
              <span class="button-icon wechat-icon"></span>
              一键微信支付
            </a-button>

            <!-- 原二维码支付，仅在非微信环境或支付宝支付时展示 -->
            <div
              class="qrcode-container"
              v-if="paymentMethod && (!isWeChatBrowser || paymentMethod === 'alipay')"
            >
              <div class="qrcode-header">
                {{ paymentMethod === 'wechat' ? '微信' : '支付宝' }}扫码支付
              </div>
              <div class="qrcode-wrapper">
                <a-spin v-if="loadingQRCode" tip="生成支付二维码..." />
                <img 
                  v-else
                  :src="qrCodeUrl" 
                  :alt="paymentMethod === 'wechat' ? '微信支付二维码' : '支付宝支付二维码'"
                />
              </div>
              <div class="qrcode-tips">
                <p class="scan-tip">请使用{{ paymentMethod === 'wechat' ? '微信' : '支付宝' }}扫一扫</p>
                <p class="payment-amount">¥{{ registrationData.totalFee.toFixed(2) }}</p>
              </div>
              <a-button type="primary" @click="confirmPayment" :loading="verifying" size="large" block>
                确认已支付
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <div class="payment-notice">
        <a-alert
          type="info"
          show-icon
          message="支付须知"
          description="请在30分钟内完成支付，超时订单将自动取消。支付成功后将收到确认短信，如有问题请联系会务组。"
        />
      </div>
    </div>

    <div class="footer-info">
      <div class="info-item">
        <i class="location-icon"></i>
        <span><strong>会议地点：</strong>郑州市XXXX酒店（XXXX路XX号）</span>
      </div>
      <div class="info-item">
        <i class="phone-icon"></i>
        <span><strong>联系方式：</strong>0371-XXXXXXXX（赵老师）</span>
      </div>
      <div class="info-item">
        <i class="org-icon"></i>
        <span><strong>主办单位：</strong>河南省软组织病研究会</span>
      </div>
    </div>

    <a-modal v-model:visible="paymentVerifying" title="支付验证中" :closable="false" :maskClosable="false" :footer="null">
      <div class="payment-verifying">
        <a-spin />
        <p>正在验证支付状态，请稍候...</p>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { generatePaymentQRCode, verifyPayment, generateJsapiPayment } from '../../api/registration'

const router = useRouter()
const paymentMethod = ref('')
const isWeChatBrowser = navigator.userAgent.toLowerCase().includes('micromessenger')
const registrationData = reactive({})
const paymentData = reactive({})
const qrCodeUrl = ref('')
const loadingQRCode = ref(false)
const verifying = ref(false)
const paymentVerifying = ref(false)
const paymentCheckInterval = ref(null)
const jsapiLoading = ref(false)
const countdownMinutes = ref(30) // 30分钟倒计时
const remainingSeconds = ref(countdownMinutes.value * 60)
const countdownInterval = ref(null)
const formatCountdown = ref('30:00')

onMounted(() => {
  // 从本地存储获取报名数据
  const storedData = localStorage.getItem('registrationData')
  if (storedData) {
    Object.assign(registrationData, JSON.parse(storedData))
  }
  const storedPay = localStorage.getItem('paymentData')
  if (storedPay) {
    Object.assign(paymentData, JSON.parse(storedPay))
  }

  // 如果没有订单ID，返回报名页面
  if (!registrationData.orderId) {
    message.error('订单信息不完整，请重新填写')
    router.push('/registration')
  }
  
  // 启动倒计时
  startCountdown()
})

// 在组件销毁时清除计时器
onUnmounted(() => {
  if (paymentCheckInterval.value) {
    clearInterval(paymentCheckInterval.value)
  }
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
  }
})

// 启动倒计时
const startCountdown = () => {
  // 清除之前的计时器
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value)
  }
  
  // 每秒减少一秒并更新显示
  countdownInterval.value = setInterval(() => {
    remainingSeconds.value -= 1
    
    // 计算分钟和秒
    const minutes = Math.floor(remainingSeconds.value / 60)
    const seconds = remainingSeconds.value % 60
    
    // 更新格式化显示
    formatCountdown.value = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    
    // 如果时间到，取消订单并提示
    if (remainingSeconds.value <= 0) {
      clearInterval(countdownInterval.value)
      message.error('订单已超时，请重新报名')
      setTimeout(() => {
        router.push('/registration')
      }, 2000)
    }
  }, 1000)
}

const handlePaymentMethodChange = async () => {
  if (!paymentMethod.value) return
  
  loadingQRCode.value = true
  qrCodeUrl.value = ''
  
  try {
    const requestData = {
      orderId: registrationData.orderId,
      paymentType: paymentMethod.value,
      amount: registrationData.totalFee
    }
    
    const res = await generatePaymentQRCode(requestData)
    
    if (res && res.code === 1 && res.data && res.data.qrCodeUrl) {
      qrCodeUrl.value = res.data.qrCodeUrl
      
      // 开始定时检查支付状态
      startPaymentStatusCheck()
    } else {
      message.error('获取支付二维码失败')
      
      // 开发环境下，使用模拟的二维码
      if (import.meta.env.DEV) {
        qrCodeUrl.value = paymentMethod.value === 'wechat' 
          ? '/qrcode-wechat.png' 
          : '/qrcode-alipay.png'
      }
    }
  } catch (error) {
    console.error('获取支付二维码失败', error)
    message.error('获取支付二维码失败，请稍后重试')
    
    // 开发环境下，使用模拟的二维码
    if (import.meta.env.DEV) {
      qrCodeUrl.value = paymentMethod.value === 'wechat' 
        ? '/qrcode-wechat.png' 
        : '/qrcode-alipay.png'
    }
  } finally {
    loadingQRCode.value = false
  }
}

// 开始定时检查支付状态
const startPaymentStatusCheck = () => {
  // 清除之前的定时器
  if (paymentCheckInterval.value) {
    clearInterval(paymentCheckInterval.value)
  }
  
  // 每10秒检查一次支付状态
  paymentCheckInterval.value = setInterval(async () => {
    try {
      const res = await verifyPayment(registrationData.orderId)
      if (res && res.code === 1 && res.data && res.data.paymentStatus === 'PAID') {
        // 支付成功
        clearInterval(paymentCheckInterval.value)
        handlePaymentSuccess()
      }
    } catch (error) {
      console.error('检查支付状态失败', error)
    }
  }, 10000)
}

const confirmPayment = async () => {
  if (!paymentMethod.value) {
    message.warning('请选择支付方式')
    return
  }
  
  verifying.value = true
  paymentVerifying.value = true
  
  try {
    // 验证支付状态
    const res = await verifyPayment(registrationData.orderId)
    
    if (res && res.code === 1 && res.data && res.data.paymentStatus === 'PAID') {
      // 支付成功
      handlePaymentSuccess()
    } else {
      // 在开发环境，允许模拟支付成功
      if (import.meta.env.DEV) {
        handlePaymentSuccess()
      } else {
        message.warning('未检测到支付完成，请完成支付后再试')
      }
    }
  } catch (error) {
    console.error('验证支付状态失败', error)
    
    // 在开发环境，允许模拟支付成功
    if (import.meta.env.DEV) {
      handlePaymentSuccess()
    } else {
      message.error('验证支付状态失败，请稍后重试')
    }
  } finally {
    verifying.value = false
    paymentVerifying.value = false
  }
}

const handlePaymentSuccess = () => {
  // 存储支付信息
  localStorage.setItem('paymentData', JSON.stringify({
    method: paymentMethod.value,
    amount: registrationData.totalFee,
    time: new Date().toISOString(),
    orderId: registrationData.orderId
  }))
  
  // 显示成功消息
  message.success('支付成功！')
  
  // 清除检查支付状态的定时器
  if (paymentCheckInterval.value) {
    clearInterval(paymentCheckInterval.value)
  }
  
  // 跳转到完成页面
  router.push('/registration/complete')
}

// 调用后端生成 JSAPI 支付参数并调起微信支付
async function startJsapiPayment() {
  if (!paymentMethod.value) return
  jsapiLoading.value = true
  try {
    const params = {
      orderId: registrationData.orderId,
      amount: registrationData.totalFee
    }
    const res = await generateJsapiPayment(params)
    if (res && res.code === 1 && res.data) {
      const payParams = {
        appId: res.data.appId,
        timeStamp: res.data.timeStamp,
        nonceStr: res.data.nonceStr,
        package: res.data.package,
        signType: res.data.signType,
        paySign: res.data.paySign
      }
      invokeWxPay(payParams)
    } else {
      message.error('生成微信支付参数失败')
    }
  } catch (err) {
    console.error('JSAPI 支付参数生成失败', err)
    message.error('生成微信支付参数失败')
  } finally {
    jsapiLoading.value = false
  }
}

// 调用微信 JSSDK
function invokeWxPay(payParams) {
  function onBridgeReady() {
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest', payParams,
      function(res) {
        if (res.err_msg === 'get_brand_wcpay_request:ok') {
          handlePaymentSuccess()
        } else {
          message.error('支付取消或失败')
        }
      }
    )
  }
  if (typeof WeixinJSBridge === 'undefined') {
    document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false)
  } else {
    onBridgeReady()
  }
}
</script>

<style scoped>
.payment-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0;
  background-color: #f5f7fa;
  color: #333;
}

.payment-banner {
  background: linear-gradient(135deg, #1890ff, #0050b3);
  color: white;
  padding: 40px 20px;
  text-align: center;
  margin-bottom: 24px;
  border-radius: 0 0 16px 16px;
}

.banner-content {
  max-width: 600px;
  margin: 0 auto;
}

.logo-wrapper {
  margin-bottom: 20px;
}

.association-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: white;
  padding: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.payment-banner h1 {
  font-size: 26px;
  margin-bottom: 8px;
  color: white;
  font-weight: 600;
}

.subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
}

.main-content {
  padding: 0 16px;
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-bottom: 8px;
}

.step.completed .step-icon {
  background-color: #52c41a;
  color: white;
}

.step.active .step-icon {
  background-color: #1890ff;
  color: white;
}

.step-text {
  font-size: 14px;
  color: #666;
}

.step.active .step-text {
  color: #1890ff;
  font-weight: 500;
}

.step-line {
  width: 60px;
  height: 1px;
  background-color: #e8e8e8;
  margin: 0 12px;
}

.registration-info-card,
.payment-methods-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 20px;
  background-color: #f0f7ff;
  border-bottom: 1px solid #e6f0fa;
  font-size: 18px;
  font-weight: 500;
  color: #1677ff;
  display: flex;
  align-items: center;
}

.info-icon,
.payment-header-icon {
  margin-right: 8px;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.card-content {
  padding: 20px;
}

.order-summary {
  display: flex;
  flex-direction: column;
}

.order-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.order-row:last-child {
  border-bottom: none;
}

.order-label {
  width: 100px;
  color: #888;
  font-size: 14px;
}

.order-value {
  flex: 1;
  color: #333;
  font-size: 14px;
}

.highlight {
  color: #1890ff;
  font-weight: 500;
}

.divider {
  height: 8px;
  margin: 4px 0;
}

.order-id {
  color: #666;
  font-family: monospace;
}

.total-row {
  margin-top: 8px;
}

.total-fee {
  color: #f5222d;
  font-size: 20px;
  font-weight: bold;
}

.payment-options {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.payment-option {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #40a9ff;
}

.payment-option.active {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.option-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.wechat-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzA5YmIwNyIgZD0iTTkuMDU4IDE4LjI4MWMtLjU2MSAwLS45NTctLjE3Ni0xLjMxOC0uMzk3bC0yLjY4IDEuMzQ5Ljc2NC0yLjMwM2MtLjkxNy0uNjQ1LTEuNDc3LTEuNDc4LTEuNDc3LTIuNDkyQzQuMzQ3IDEyLjM4MiA2LjUyIDEwLjUgOS4wNTggMTAuNXM0LjcxMSAxLjg4MiA0LjcxMSAzLjkzOGMwIDIuMDU3LTIuMTcyIDMuOTM4LTQuNzExIDMuOTM4em0tLjUxLTQuNzQzYy0uMzA4IDAtLjU2Mi4yNS0uNTYyLjU1NmMwIC4zMDguMjU0LjU1Ny41NjIuNTU3cy41NjItLjI1LjU2Mi0uNTU3YzAtLjMwNi0uMjU0LS41NTYtLjU2Mi0uNTU2em0yLjg1Ni4wMDJjLS4zMDcgMC0uNTYxLjI1LS41NjEuNTU1YzAgLjMwOC4yNTQuNTU3LjU2Mi41NTdzLjU2Mi0uMjUuNTYyLS41NTdjMC0uMzA2LS4yNTUtLjU1NS0uNTYyLS41NTV6TTUuNSA3QzUuNSA0LjUxNSA4LjE2NiAyLjUgMTEuNSAyLjVzNiAyLjAxNSA2IDQuNWMwIDIuNDg2LTIuNjY2IDQuNS02IDQuNXMtNi0yLjAxNC02LTQuNXptMy40MDQtLjg3MmMuMzc4IDAgLjY4NC4zMDYuNjg0LjY4M2ExLjY4IDEuNjggMCAwIDEtLjY4NC42ODRhLjY4My42ODMgMCAwIDEtLjY4My0uNjg0Yy4wMDEtLjM3Ny4zMDYtLjY4My42ODMtLjY4M3ptNC4xOTIgMGMuMzc4IDAgLjY4NC4zMDYuNjg0LjY4M2ExLjY4IDEuNjggMCAwIDEtLjY4NC42ODRhLjY4My42ODMgMCAwIDEtLjY4My0uNjg0Yy4wMDEtLjM3Ny4zMDYtLjY4My42ODMtLjY4M3oiLz48L3N2Zz4=');
}

.alipay-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzE2NzdmZiIgZD0iTTEyLjA5IDEyLjYwMWMtLjYxLS40ODktMy40MDEtMi40MTQtNC45MjMtLjMzMy0xLjI0IDEuNjkzLS40MDMgNC4zMzUgMi4wOTcgNC4zMzVjMS44MTYgMCAzLjEyNC0xLjQyIDMuOTk3LTIuMTc2Yy4yOC0uMjQyLjUwNy0uNDM2LjY4Mi0uNDM2YzEuMDM2IDAgNS4yMzIgMi42NjMgNi4yMDcgMy4xMjRjMCAwIDEuMjM5LjU0NCAxLjEyNi0uNTgxYy0uMTE0LTEuMTI1LS44NC0yLjg1LTEuMjM5LTMuNzU3Yy0uMzk4LS45MDctMS4wMDktMi4yMS0xLjIyNi0yLjkxOGMtLjMwNi0uOTk0LS4yNzctLjkwNy0xLjA4Ni0yLjA5M2EuNTQ1LjU0NSAwIDAgMC0uMTQ4LS4xOTZjLS41MDQuNTIxLTEuODM2IDEuOTQtMi45MyAyLjI0MkM4LjY2MiAxMC45OCA0LjA0MSAxMC4zNjEgNC4wNDEgMTAuMzYxUzMuMDcxIDEwLjI5MiAzIDExLjM5YzEuNjI0LS4wMjMgNi4xMDEuNiA4LjAwOCAxLjY1NGMxLjAyNC41NjYgMS45OTguNzg2IDMuMDA1LjMxMXMtMS4wNjMtLjI2Ni0xLjkyMy0uNzU0ek0xOC4zMjUgNC43NzJMNy43MyAyLjU2cy0uNjczLS4yNzMtLjc4OS42MjFjLS4xMTYuODk1LjUyNSAxLjAxOC41MjUgMS4wMThsNy44OTMgMi4zODFjLjEwOS4wMzIuMjc3LjA2OC4yNTUuMThzLS4xNzguMTM2LS4yNDguMTY3bC0xMi42NjggNS4wNjRTMi40MDEgMTIuMDM3IDMgMTIuNTk0YzEgLjkzIDkuOTI5IDIuODg4IDExLjk3IDMuNDZzMS45NjIuNDMyIDMuMzE4LS40MjFjMS4zNTYtLjg1MyAxLjQ2Mi0xLjM3MiAyLjEzNS0yLjIxbC01LjQ5MS0yLjg5OWMtLjA2NS0uMDM1LS4xOTctLjA5NS0uMTk1LS4xNjcgMC0uMDc0LjEzLS4xMzcuMTk1LS4xNjZsOC41ODItMy4yMzVzLjY0LS4yODguNDEtLjgyN2MtLjIzLS41NC0uNi0uMzU3LS42LS4zNTd6Ii8+PC9zdmc+');
}

.option-name {
  font-size: 16px;
}

.payment-content {
  margin-top: 20px;
}

.jsapi-button {
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

.button-icon {
  margin-right: 8px;
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.qrcode-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
  padding-bottom: 20px;
}

.qrcode-header {
  background-color: #f5f7fa;
  padding: 12px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
}

.qrcode-wrapper {
  width: 200px;
  height: 200px;
  margin: 24px auto;
  background: white;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrcode-wrapper img {
  max-width: 180px;
  max-height: 180px;
}

.qrcode-tips {
  text-align: center;
  margin-bottom: 20px;
}

.scan-tip {
  color: #666;
  margin-bottom: 8px;
}

.payment-amount {
  font-size: 24px;
  font-weight: bold;
  color: #f5222d;
  margin-bottom: 20px;
}

.payment-notice {
  margin: 24px 0;
}

.footer-info {
  background-color: #fff;
  padding: 20px;
  text-align: center;
  border-radius: 12px 12px 0 0;
  margin-top: 40px;
  border-top: 1px solid #eaeaea;
}

.info-item {
  margin-bottom: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-icon,
.phone-icon,
.org-icon {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.payment-verifying {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.payment-verifying p {
  margin-top: 16px;
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .payment-banner {
    padding: 30px 15px;
  }
  
  .payment-banner h1 {
    font-size: 22px;
  }
  
  .subtitle {
    font-size: 14px;
  }
  
  .association-logo {
    width: 60px;
    height: 60px;
  }
  
  .main-content {
    padding: 0 12px;
  }
  
  .step-line {
    width: 30px;
  }
  
  .step-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .step-text {
    font-size: 12px;
  }
  
  .payment-options {
    flex-direction: column;
    gap: 10px;
  }
  
  .card-content {
    padding: 15px;
  }
  
  .qrcode-wrapper {
    width: 180px;
    height: 180px;
  }
  
  .qrcode-wrapper img {
    max-width: 160px;
    max-height: 160px;
  }
}

.order-countdown {
  margin-bottom: 20px;
}

.countdown-title {
  font-weight: 500;
}

.time-remaining {
  font-weight: bold;
  color: #ff4d4f;
  font-size: 16px;
  font-family: monospace;
}

.countdown-desc {
  color: #999;
  font-size: 14px;
}
</style> 
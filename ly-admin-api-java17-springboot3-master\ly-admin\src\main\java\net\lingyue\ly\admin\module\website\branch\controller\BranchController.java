package net.lingyue.ly.admin.module.website.branch.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchAddForm;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchQueryForm;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchUpdateForm;
import net.lingyue.ly.admin.module.website.branch.domain.vo.BranchVO;
import net.lingyue.ly.admin.module.website.branch.service.BranchService;
import net.lingyue.ly.base.common.annoation.NoNeedLogin;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;

/**
 * 分会管理
 */
@Tag(name = AdminSwaggerTagConst.Website.BRANCH)
@RestController
@OperateLog
public class BranchController {

    @Resource
    private BranchService branchService;

    @Operation(summary = "分会-添加 @author")
    @PostMapping("/website/branch/add")
    @SaCheckPermission("website:branch:add")
    public ResponseDTO<String> addBranch(@RequestBody @Valid BranchAddForm addForm) {
        return branchService.add(addForm);
    }

    @Operation(summary = "分会-修改 @author")
    @PostMapping("/website/branch/update")
    @SaCheckPermission("website:branch:update")
    public ResponseDTO<String> updateBranch(@RequestBody @Valid BranchUpdateForm updateForm) {
        return branchService.update(updateForm);
    }

    @Operation(summary = "分会-删除 @author")
    @GetMapping("/website/branch/delete/{branchId}")
    @SaCheckPermission("website:branch:delete")
    public ResponseDTO<String> deleteBranch(@PathVariable Long branchId) {
        return branchService.delete(branchId);
    }

    @Operation(summary = "分会-详情 @author")
    @GetMapping("/website/branch/get/{branchId}")
    @SaCheckPermission("website:branch:query")
    public ResponseDTO<BranchVO> getBranch(@PathVariable Long branchId) {
        return branchService.getDetail(branchId);
    }

    @Operation(summary = "分会-分页查询 @author")
    @PostMapping("/website/branch/page")
    @SaCheckPermission("website:branch:query")
    public ResponseDTO<PageResult<BranchVO>> pageBranch(@RequestBody BranchQueryForm queryForm) {
        return ResponseDTO.ok(branchService.queryPage(queryForm));
    }
    @NoNeedLogin
    @Operation(summary = "分会-获取全部 @author")
    @GetMapping("/website/branch/getAll")
    public ResponseDTO<List<BranchVO>> getAllBranch() {
        return ResponseDTO.ok(branchService.getAll());
    }
}

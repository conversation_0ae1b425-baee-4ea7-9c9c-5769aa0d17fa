package net.lingyue.ly.admin.module.website.content.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.module.website.content.constant.ContentStatusEnum;
import net.lingyue.ly.admin.module.website.content.dao.ContentDao;
import net.lingyue.ly.admin.module.website.content.dao.ContentTypeDao;
import net.lingyue.ly.admin.module.website.content.dao.ContentCategoryRelationDao;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentTypeEntity;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentAddForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentUpdateForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentDetailVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentUpdateFormVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentVO;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartBeanUtil;
import net.lingyue.ly.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容服务
 */
@Slf4j
@Service
public class ContentService {

    @Resource
    private ContentDao contentDao;

    @Resource
    private ContentTypeDao contentTypeDao;

    @Resource
    private ContentCategoryRelationDao contentCategoryRelationDao;

    /**
     * 分页查询内容
     *
     * @param queryForm 查询表单
     * @return 内容列表
     */
    public PageResult<ContentVO> query(ContentQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ContentVO> contentList = contentDao.queryPage(page, queryForm);

        // 设置状态名称
        contentList.forEach(contentVO -> {
            contentVO.setStatusName(ContentStatusEnum.getDescByValue(contentVO.getStatus()));
            contentVO.setPublishFlag(contentVO.getStatus().equals(ContentStatusEnum.PUBLISHED.getValue()));
        });

        return SmartPageUtil.convert2PageResult(page, contentList);
    }

    /**
     * 添加内容
     *
     * @param addForm 添加表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ContentAddForm addForm) {
        ContentEntity contentEntity = SmartBeanUtil.copy(addForm, ContentEntity.class);

        // 设置默认值
        contentEntity.setPageViewCount(0);
        contentEntity.setDeletedFlag(false);

        // 处理附件
        if (CollUtil.isNotEmpty(addForm.getAttachment())) {
            contentEntity.setAttachment(StrUtil.join(",", addForm.getAttachment()));
        }

        // 处理发布时间
        if (addForm.getScheduledPublishFlag() != null && addForm.getScheduledPublishFlag()) {
            if (addForm.getPublishTime() == null) {
                contentEntity.setPublishTime(LocalDateTime.now());
            }
        } else {
            contentEntity.setPublishTime(LocalDateTime.now());
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        contentEntity.setCreateTime(now);
        contentEntity.setUpdateTime(now);

        // 插入内容
        contentDao.insert(contentEntity);

        // 处理分类关联
        if (CollUtil.isNotEmpty(addForm.getCategoryIds())) {
            contentCategoryRelationDao.batchInsert(contentEntity.getContentId(), addForm.getCategoryIds());
        }

        return ResponseDTO.ok();
    }

    /**
     * 更新内容
     *
     * @param updateForm 更新表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ContentUpdateForm updateForm) {
        ContentEntity contentEntity = contentDao.selectById(updateForm.getContentId());
        if (contentEntity == null) {
            return ResponseDTO.userErrorParam("内容不存在");
        }

        BeanUtil.copyProperties(updateForm, contentEntity);

        // 处理附件
        if (CollUtil.isNotEmpty(updateForm.getAttachment())) {
            contentEntity.setAttachment(StrUtil.join(",", updateForm.getAttachment()));
        } else {
            contentEntity.setAttachment(null);
        }

        // 处理发布时间
        if (updateForm.getScheduledPublishFlag() != null && updateForm.getScheduledPublishFlag()) {
            if (updateForm.getPublishTime() == null) {
                contentEntity.setPublishTime(LocalDateTime.now());
            }
        } else {
            contentEntity.setPublishTime(LocalDateTime.now());
        }

        // 设置更新时间
        contentEntity.setUpdateTime(LocalDateTime.now());

        // 更新内容
        contentDao.updateById(contentEntity);

        // 处理分类关联
        if (updateForm.getCategoryIds() != null) {
            // 先删除旧的关联
            contentCategoryRelationDao.deleteByContentId(contentEntity.getContentId());

            // 添加新的关联
            if (CollUtil.isNotEmpty(updateForm.getCategoryIds())) {
                contentCategoryRelationDao.batchInsert(contentEntity.getContentId(), updateForm.getCategoryIds());
            }
        }

        return ResponseDTO.ok();
    }

    /**
     * 获取更新表单VO
     *
     * @param contentId 内容ID
     * @return 更新表单VO
     */
    public ContentUpdateFormVO getUpdateFormVO(Long contentId) {
        ContentEntity contentEntity = contentDao.selectById(contentId);
        if (contentEntity == null) {
            return null;
        }

        ContentUpdateFormVO updateFormVO = SmartBeanUtil.copy(contentEntity, ContentUpdateFormVO.class);

        // 处理附件
        if (StrUtil.isNotBlank(contentEntity.getAttachment())) {
            List<String> attachmentList = StrUtil.split(contentEntity.getAttachment(), ',');
            updateFormVO.setAttachment(attachmentList.stream().map(item -> (Object) item).collect(Collectors.toList()));
        }

        // 获取分类ID列表
        List<Long> categoryIds = contentCategoryRelationDao.selectCategoryIdsByContentId(contentId);
        updateFormVO.setCategoryIds(categoryIds);

        return updateFormVO;
    }

    /**
     * 获取内容详情
     *
     * @param contentId 内容ID
     * @return 内容详情
     */
    public ContentDetailVO getDetail(Long contentId) {
        ContentEntity contentEntity = contentDao.selectById(contentId);
        if (contentEntity == null) {
            return null;
        }

        ContentDetailVO detailVO = SmartBeanUtil.copy(contentEntity, ContentDetailVO.class);

        // 设置状态名称
        detailVO.setStatusName(ContentStatusEnum.getDescByValue(detailVO.getStatus()));

        // 处理附件
        if (StrUtil.isNotBlank(contentEntity.getAttachment())) {
            List<String> attachmentList = StrUtil.split(contentEntity.getAttachment(), ',');
            detailVO.setAttachment(attachmentList.stream().map(item -> (Object) item).collect(Collectors.toList()));
        }

        // 获取分类ID列表
        List<Long> categoryIds = contentCategoryRelationDao.selectCategoryIdsByContentId(contentId);
        detailVO.setCategoryIds(categoryIds);

        return detailVO;
    }

    /**
     * 删除内容
     *
     * @param contentId 内容ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long contentId) {
        ContentEntity contentEntity = contentDao.selectById(contentId);
        if (contentEntity == null) {
            return ResponseDTO.userErrorParam("内容不存在");
        }

        contentEntity.setDeletedFlag(true);
        contentEntity.setUpdateTime(LocalDateTime.now());

        contentDao.updateById(contentEntity);
        return ResponseDTO.ok();
    }

    /**
     * 增加页面浏览量
     *
     * @param contentId 内容ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void increasePageViewCount(Long contentId) {
        ContentEntity contentEntity = contentDao.selectById(contentId);
        if (contentEntity == null) {
            return;
        }

        contentEntity.setPageViewCount(contentEntity.getPageViewCount() + 1);
        contentEntity.setUpdateTime(LocalDateTime.now());

        contentDao.updateById(contentEntity);
    }

    /**
     * 获取推荐内容列表
     *
     * @param limit 限制数量
     * @return 推荐内容列表
     */
    public List<ContentVO> getRecommendContent(Integer limit) {
        LambdaQueryWrapper<ContentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentEntity::getRecommendFlag, true)
                .eq(ContentEntity::getStatus, ContentStatusEnum.PUBLISHED.getValue())
                .eq(ContentEntity::getDeletedFlag, false)
                .orderByDesc(ContentEntity::getPublishTime)
                .last("LIMIT " + limit);

        List<ContentEntity> contentList = contentDao.selectList(queryWrapper);
        List<ContentVO> contentVOList = SmartBeanUtil.copyList(contentList, ContentVO.class);

        // 设置内容类型名称
        setContentTypeNames(contentVOList);

        return contentVOList;
    }

    /**
     * 获取置顶内容列表
     *
     * @param limit 限制数量
     * @return 置顶内容列表
     */
    public List<ContentVO> getTopContent(Integer limit) {
        LambdaQueryWrapper<ContentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentEntity::getTopFlag, true)
                .eq(ContentEntity::getStatus, ContentStatusEnum.PUBLISHED.getValue())
                .eq(ContentEntity::getDeletedFlag, false)
                .orderByDesc(ContentEntity::getPublishTime)
                .last("LIMIT " + limit);

        List<ContentEntity> contentList = contentDao.selectList(queryWrapper);
        List<ContentVO> contentVOList = SmartBeanUtil.copyList(contentList, ContentVO.class);

        // 设置内容类型名称
        setContentTypeNames(contentVOList);

        return contentVOList;
    }

    /**
     * 根据内容类型获取内容列表
     *
     * @param contentTypeId 内容类型ID
     * @param limit 限制数量
     * @return 内容列表
     */
    public List<ContentVO> getContentByType(Long contentTypeId, Integer limit) {
        LambdaQueryWrapper<ContentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentEntity::getContentTypeId, contentTypeId)
                .eq(ContentEntity::getStatus, ContentStatusEnum.PUBLISHED.getValue())
                .eq(ContentEntity::getDeletedFlag, false)
                .orderByDesc(ContentEntity::getTopFlag)
                .orderByDesc(ContentEntity::getPublishTime)
                .last("LIMIT " + limit);

        List<ContentEntity> contentList = contentDao.selectList(queryWrapper);
        List<ContentVO> contentVOList = SmartBeanUtil.copyList(contentList, ContentVO.class);

        // 设置内容类型名称
        setContentTypeNames(contentVOList);

        return contentVOList;
    }

    /**
     * 设置内容类型名称
     *
     * @param contentVOList 内容VO列表
     */
    private void setContentTypeNames(List<ContentVO> contentVOList) {
        if (CollUtil.isEmpty(contentVOList)) {
            return;
        }

        // 获取所有内容类型ID
        List<Long> contentTypeIds = contentVOList.stream()
                .map(ContentVO::getContentTypeId)
                .distinct()
                .collect(Collectors.toList());

        // 查询内容类型
        LambdaQueryWrapper<ContentTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ContentTypeEntity::getContentTypeId, contentTypeIds);
        List<ContentTypeEntity> contentTypeList = contentTypeDao.selectList(queryWrapper);

        // 构建内容类型ID到名称的映射
        java.util.Map<Long, String> contentTypeMap = contentTypeList.stream()
                .collect(Collectors.toMap(ContentTypeEntity::getContentTypeId, ContentTypeEntity::getContentTypeName));

        // 设置内容类型名称
        contentVOList.forEach(contentVO -> {
            contentVO.setContentTypeName(contentTypeMap.get(contentVO.getContentTypeId()));
            contentVO.setStatusName(ContentStatusEnum.getDescByValue(contentVO.getStatus()));
            contentVO.setPublishFlag(contentVO.getStatus().equals(ContentStatusEnum.PUBLISHED.getValue()));
        });
    }
}

<template>
  <ContentDetail 
    :detail="partyDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getDetailConfig } from '../../utils/detailConfig'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const partyDetail = ref({})

// 详情页配置
const detailConfig = getDetailConfig('party')

// 获取党建动态详情
const fetchPartyDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id || route.query.id
    if (!id) {
      message.error('缺少党建动态ID参数')
      return
    }
    
    const response = await getContentDetail(id)
    if (response && response.code === 0 && response.data) {
      partyDetail.value = response.data
    } else {
      message.error(response?.msg || '获取党建动态详情失败')
    }
  } catch (error) {
    console.error('获取党建动态详情失败:', error)
    message.error('获取党建动态详情失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchPartyDetail()
})
</script>

<style scoped>
.party-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20px 0;
}
</style>
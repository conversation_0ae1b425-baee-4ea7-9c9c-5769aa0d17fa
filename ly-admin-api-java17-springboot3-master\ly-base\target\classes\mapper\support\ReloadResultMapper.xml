<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.base.module.support.reload.dao.ReloadResultDao">

    <!-- 查询reload列表 -->
    <select id="query" resultType="net.lingyue.ly.base.module.support.reload.domain.ReloadResultVO">
        SELECT tag, identification, args, result, exception, create_time FROM t_reload_result where tag = #{tag} order
        by create_time desc
    </select>

</mapper>

<!--
  * 网站基本信息配置
-->
<template>
  <div class="basic-config-container">
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="网站名称" name="websiteName">
          <a-input v-model:value="formData.websiteName" placeholder="请输入网站名称" />
        </a-form-item>

        <a-form-item label="网站LOGO" name="websiteLogo">
          <a-input v-model:value="formData.websiteLogo" placeholder="请输入网站LOGO URL" />
          <div class="upload-wrapper">
            <SmartUpload
              :limit="1"
              :file-type="FILE_FOLDER_TYPE_ENUM.COMMON.value"
              @upload-success="handleLogoUploadSuccess"
            />
            <div class="preview-image" v-if="formData.websiteLogo">
              <img :src="formData.websiteLogo" alt="网站LOGO预览" />
            </div>
          </div>
        </a-form-item>

        <a-form-item label="统一社会信用代码" name="socialCreditCode">
          <a-input v-model:value="formData.socialCreditCode" placeholder="请输入统一社会信用代码" />
        </a-form-item>

        <a-form-item label="机构名称" name="organizationName">
          <a-input v-model:value="formData.organizationName" placeholder="请输入机构名称，如：河南省软组织病研究会" />
        </a-form-item>

        <a-form-item label="机构英文名称" name="organizationEnglishName">
          <a-input v-model:value="formData.organizationEnglishName" placeholder="请输入机构英文名称" />
        </a-form-item>

        <a-form-item label="ICP备案号" name="icpNumber">
          <a-input v-model:value="formData.icpNumber" placeholder="请输入ICP备案号" />
        </a-form-item>

        <a-form-item label="公网安备号" name="publicSecurityNumber">
          <a-input v-model:value="formData.publicSecurityNumber" placeholder="请输入公网安备号" />
        </a-form-item>

        <a-form-item label="版权信息" name="copyrightInfo">
          <a-textarea v-model:value="formData.copyrightInfo" placeholder="请输入版权信息" :rows="3" />
        </a-form-item>

        <a-form-item label="法律顾问信息" name="legalAdvisorInfo">
          <a-textarea v-model:value="formData.legalAdvisorInfo" placeholder="请输入法律顾问信息" :rows="3" />
        </a-form-item>

        <!-- 地图配置部分 -->
        <a-divider orientation="left">地图配置</a-divider>

        <a-form-item label="地图API密钥" name="mapApiKey">
          <a-input v-model:value="formData.mapApiKey" placeholder="请输入高德地图API密钥" />
          <div class="form-item-help">用于加载高德地图，申请地址：https://lbs.amap.com/</div>
        </a-form-item>

        <a-form-item label="地图安全密钥" name="mapSecurityCode">
          <a-input v-model:value="formData.mapSecurityCode" placeholder="请输入高德地图安全密钥" />
          <div class="form-item-help">用于加载高德地图，申请地址：https://lbs.amap.com/</div>
        </a-form-item>

        <a-form-item label="地图中心点" required>
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item name="mapCenterLongitude" :style="{ marginBottom: 0 }">
                <a-input-number
                  v-model:value="formData.mapCenterLongitude"
                  placeholder="经度"
                  :min="-180"
                  :max="180"
                  :precision="6"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="mapCenterLatitude" :style="{ marginBottom: 0 }">
                <a-input-number
                  v-model:value="formData.mapCenterLatitude"
                  placeholder="纬度"
                  :min="-90"
                  :max="90"
                  :precision="6"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="form-item-help">
            可以使用高德地图坐标拾取工具获取：
            <a href="https://lbs.amap.com/tools/picker" target="_blank">https://lbs.amap.com/tools/picker</a>
          </div>
        </a-form-item>

        <a-form-item label="地图缩放级别" name="mapZoom">
          <a-slider
            v-model:value="formData.mapZoom"
            :min="3"
            :max="18"
            :step="1"
            :marks="{
              3: '3',
              6: '6',
              9: '9',
              12: '12',
              15: '15',
              18: '18'
            }"
          />
        </a-form-item>

        <a-form-item label="地图标记点标题" name="mapMarkerTitle">
          <a-input v-model:value="formData.mapMarkerTitle" placeholder="请输入地图标记点标题" />
        </a-form-item>

        <a-form-item label="地图标记点地址" name="mapMarkerAddress">
          <a-input v-model:value="formData.mapMarkerAddress" placeholder="请输入地图标记点地址" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 16, offset: 4 }">
          <a-button type="primary" @click="handleSubmit">保存</a-button>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { websiteConfigApi } from '/@/api/website/website-config-api';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import SmartUpload from '/@/components/framework/smart-upload/index.vue';

const formRef = ref();
const loading = ref(false);
const formData = ref({
  websiteName: '',
  websiteLogo: '',
  socialCreditCode: '',
  organizationName: '',
  organizationEnglishName: '',
  icpNumber: '',
  publicSecurityNumber: '',
  copyrightInfo: '',
  legalAdvisorInfo: '',
  // 地图配置
  mapApiKey: '',
  mapSecurityCode: '',
  mapCenterLongitude: 113.665412,
  mapCenterLatitude: 34.757975,
  mapZoom: 15,
  mapMarkerTitle: '',
  mapMarkerAddress: ''
});

const rules = {
  websiteName: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
  organizationName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
  mapCenterLongitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
  mapCenterLatitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
  mapZoom: [{ required: true, message: '请选择缩放级别', trigger: 'change' }],
  mapMarkerTitle: [{ required: true, message: '请输入标记点标题', trigger: 'blur' }],
  mapMarkerAddress: [{ required: true, message: '请输入标记点地址', trigger: 'blur' }]
};

// 获取基本信息配置
const fetchConfig = async () => {
  loading.value = true;
  try {
    const res = await websiteConfigApi.getWebsiteBasicConfig();
    if (res.data) {
      formData.value = { ...formData.value, ...res.data };
    }
  } catch (error) {
    console.error('获取基本信息配置失败:', error);
    message.error('获取基本信息配置失败');
  } finally {
    loading.value = false;
  }
};

// 处理LOGO上传成功
const handleLogoUploadSuccess = (fileList) => {
  console.log('LOGO上传成功，返回数据:', fileList);
  if (fileList && fileList.length > 0) {
    const file = fileList[0];
    // 优先使用 fileUrl，如果没有则尝试使用 url
    if (file.fileUrl) {
      formData.value.websiteLogo = file.fileUrl;
      console.log('使用 fileUrl:', file.fileUrl);
    } else if (file.url) {
      formData.value.websiteLogo = file.url;
      console.log('使用 url:', file.url);
    } else {
      console.error('LOGO上传成功但未获取到URL:', file);
      message.warning('图片上传成功，但未获取到URL');
      return;
    }

    // 手动触发表单验证
    if (formRef.value) {
      setTimeout(() => {
        formRef.value.validateFields(['websiteLogo']);
      }, 100);
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  formRef.value.validate().then(async () => {
    loading.value = true;
    try {
      const res = await websiteConfigApi.updateWebsiteBasicConfig(formData.value);
      if (res.code === 0) {
        message.success('保存成功');
      } else {
        message.error(res.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存基本信息配置失败:', error);
      message.error('保存失败');
    } finally {
      loading.value = false;
    }
  });
};

onMounted(() => {
  fetchConfig();
});

defineExpose({
  fetchConfig
});
</script>

<style lang="less" scoped>
.basic-config-container {
  padding: 20px;
}

.upload-wrapper {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.preview-image {
  margin-left: 20px;
  width: 160px;
  height: 90px;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.form-item-help {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>

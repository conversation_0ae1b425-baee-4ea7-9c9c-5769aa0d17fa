<template>
  <div class="representative-detail-page">
    <div class="container">
      <h1 class="page-title">分支机构</h1>
      <div class="page-content">
        <ContentDetail 
          :detail="representativeDetail" 
          :loading="loading" image.png
          :config="detailConfig"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getRepresentativeDetail } from '../../api/representative'
import ContentDetail from '../../components/common/ContentDetail.vue'
import { representativeDetailConfig } from '../../utils/detailConfig'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const representativeDetail = ref({})

// 详情配置，添加自定义的内容字段处理
const detailConfig = {
  ...representativeDetailConfig,
  contentField: 'content', // 如果没有content字段，可以使用description
  backHandler: () => {
    router.push('/representative')
  }
}

// 获取代表处详情
const fetchRepresentativeDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id || route.query.id
    if (!id) {
      message.error('缺少代表处ID参数')
      return
    }

               const res = await getRepresentativeDetail(id)
           // 兼容不同的响应格式：code: 0, 1, 200 都表示成功
           if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
             // 如果没有content字段，使用description
             if (!res.data.content && res.data.description) {
               res.data.content = res.data.description
             }
             representativeDetail.value = res.data
           } else {
             console.error('获取代表处详情失败，响应:', res)
             message.error('获取代表处详情失败')
           }
  } catch (error) {
    console.error('获取代表处详情失败:', error)
    message.error('获取代表处详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchRepresentativeDetail()
})
</script>

<style scoped>
.representative-detail-page {
  width: 100%;
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
}

.detail-breadcrumb {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

.detail-breadcrumb a {
  color: #666;
  text-decoration: none;
}

.detail-breadcrumb a:hover {
  color: var(--primary-color);
}

.page-content {
  margin-top: 20px;
}
</style>

package net.lingyue.ly.admin.module.website.content.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lingyue.ly.base.common.enumeration.BaseEnum;

/**
 * 内容状态枚举
 */
@Getter
@AllArgsConstructor
public enum ContentStatusEnum implements BaseEnum {

    DRAFT(0, "草稿"),
    PUBLISHED(1, "已发布"),
    OFFLINE(2, "已下线");

    private final Integer value;
    private final String desc;

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return "";
        }
        for (ContentStatusEnum statusEnum : ContentStatusEnum.values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}

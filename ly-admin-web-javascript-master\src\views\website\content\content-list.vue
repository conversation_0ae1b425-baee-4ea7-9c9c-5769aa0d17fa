<!--
  * 内容管理列表
-->
<template>
  <a-form class="smart-query-form" v-privilege="'website:content:query'">
    <a-row class="smart-query-form-row">
      <a-form-item label="分类" class="smart-query-form-item">
        <a-select v-model:value="queryForm.contentTypeId" style="width: 120px" :showSearch="true" :allowClear="true" placeholder="分类">
          <a-select-option v-for="item in contentTypeList" :key="item.contentTypeId" :value="item.contentTypeId">
            {{ item.contentTypeName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="分会" class="smart-query-form-item">
        <a-select v-model:value="queryForm.branchId" style="width: 120px" :showSearch="true" :allowClear="true" placeholder="分会">
          <a-select-option v-for="item in branchList" :key="item.branchId" :value="item.branchId">
            {{ item.branchName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="内容分类" class="smart-query-form-item">
        <a-tree-select
          v-model:value="queryForm.categoryId"
          style="width: 150px"
          :tree-data="categoryTreeData"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="内容分类"
          allow-clear
          tree-default-expand-all
        />
      </a-form-item>

      <a-form-item label="关键字" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.keywords" placeholder="标题、作者、来源" />
      </a-form-item>

      <a-form-item label="状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status" style="width: 100px" :allowClear="true" placeholder="状态">
          <a-select-option v-for="(value, key) in CONTENT_STATUS_ENUM" :key="key" :value="value.value">
            {{ value.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否置顶" class="smart-query-form-item">
        <SmartBooleanSelect v-model:value="queryForm.topFlag" style="width: 70px" />
      </a-form-item>

      <a-form-item label="是否推荐" class="smart-query-form-item">
        <SmartBooleanSelect v-model:value="queryForm.recommendFlag" style="width: 70px" />
      </a-form-item>

      <a-form-item label="是否删除" class="smart-query-form-item">
        <SmartBooleanSelect v-model:value="queryForm.deletedFlag" style="width: 70px" />
      </a-form-item>

      <a-form-item label="发布时间" class="smart-query-form-item">
        <a-range-picker v-model:value="publishDate" :presets="defaultTimeRanges" @change="publishDateChange" style="width: 220px" />
      </a-form-item>

      <a-form-item label="创建时间" class="smart-query-form-item">
        <a-range-picker v-model:value="createDate" :presets="defaultTimeRanges" @change="createDateChange" style="width: 220px" />
      </a-form-item>

      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="onReload">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="addOrUpdate()" v-privilege="'website:content:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button type="primary" @click="showContentTypeModal()" v-privilege="'website:content:type:query'">
          <template #icon>
            <AppstoreOutlined />
          </template>
          内容分类
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="tableColumns" :tableId="TABLE_ID_CONST.WEBSITE.CONTENT" :refresh="queryContentList" />
      </div>
    </a-row>

    <a-table
      rowKey="contentId"
      :columns="tableColumns"
      :dataSource="tableData"
      :scroll="{ x: 1510 }"
      :pagination="false"
      :loading="tableLoading"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'title'">
          <a @click="toDetail(record.contentId)">{{ text }}</a>
        </template>
        <template v-else-if="column.dataIndex === 'topFlag'">
          <a-tag v-show="text" color="success">置顶</a-tag>
          <a-tag v-show="!text" color="default">普通</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'recommendFlag'">
          <a-tag v-show="text" color="success">推荐</a-tag>
          <a-tag v-show="!text" color="default">普通</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'status'">
          <a-tag v-if="text === CONTENT_STATUS_ENUM.DRAFT.value" color="warning">草稿</a-tag>
          <a-tag v-else-if="text === CONTENT_STATUS_ENUM.PUBLISHED.value" color="success">已发布</a-tag>
          <a-tag v-else-if="text === CONTENT_STATUS_ENUM.OFFLINE.value" color="error">已下线</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'deletedFlag'">
          <a-tag v-show="text" color="error">已删除</a-tag>
          <a-tag v-show="!text" color="success">未删除</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div class="smart-table-operate" v-if="!record.deletedFlag">
            <a-button type="link" @click="addOrUpdate(record.contentId)" v-privilege="'website:content:update'">编辑</a-button>
            <a-button type="link" @click="onDelete(record.contentId)" v-privilege="'website:content:delete'" danger>删除</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryContentList"
        @showSizeChange="queryContentList"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <ContentFormDrawer ref="contentFormDrawer" @reloadList="queryContentList" />
  <ContentTypeModal ref="contentTypeModal" @reloadList="queryContentTypeList" />
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { CONTENT_STATUS_ENUM } from '/@/constants/website/content-const';
  import SmartBooleanSelect from '/@/components/framework/boolean-select/index.vue';
  import { contentApi } from '/@/api/website/content-api';
  import ContentFormDrawer from './components/content-form-drawer.vue';
  import ContentTypeModal from './components/content-type-modal.vue';
  import { defaultTimeRanges } from '/@/lib/default-time-ranges';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import { getAllBranch } from '/@/api/website/branch';
  import { getContentCategoryTree } from '/@/api/website/content';

  const queryFormState = {
    contentTypeId: undefined, //分类
    branchId: undefined, //分会
    categoryId: undefined, //内容分类
    keywords: '', //标题、作者、来源
    status: undefined, //状态
    topFlag: undefined, //是否置顶
    recommendFlag: undefined, //是否推荐
    deletedFlag: undefined, //删除标识
    createTimeBegin: null, //创建-开始时间
    createTimeEnd: null, //创建-截止时间
    publishTimeBegin: null, //发布-开始时间
    publishTimeEnd: null, //发布-截止时间
    pageNum: 1,
    pageSize: PAGE_SIZE,
  };
  const queryForm = reactive({ ...queryFormState });

  const tableColumns = ref([
    {
      title: `标题`,
      dataIndex: 'title',
      width: 300,
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'contentTypeName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '分会',
      dataIndex: 'branchName',
      width: 100,
      ellipsis: true,
    },
    {
      title: `作者`,
      dataIndex: 'author',
      width: 80,
      ellipsis: true,
    },
    {
      title: `来源`,
      dataIndex: 'source',
      width: 90,
      ellipsis: true,
    },
    {
      title: '置顶',
      dataIndex: 'topFlag',
      width: 60,
    },
    {
      title: '推荐',
      dataIndex: 'recommendFlag',
      width: 60,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
    },
    {
      title: '删除',
      dataIndex: 'deletedFlag',
      width: 80,
    },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      width: 150,
    },
    {
      title: '页面浏览量',
      dataIndex: 'pageViewCount',
      width: 90,
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
    },
  ]);

  // ------------------ 内容分类 ------------------

  // 查询分类列表
  const contentTypeList = ref([]);
  async function queryContentTypeList() {
    try {
      const result = await contentApi.getAllContentTypes();
      contentTypeList.value = result.data;
    } catch (err) {
      smartSentry.captureError(err);
    }
  }

  // 显示内容分类管理弹窗
  const contentTypeModal = ref();
  function showContentTypeModal() {
    if (contentTypeModal.value) {
      contentTypeModal.value.showModal();
    } else {
      message.error('组件未初始化完成，请稍后再试');
    }
  }

  // ------------------ 查询相关 ------------------

  const tableData = ref([]);
  const total = ref(0);
  const tableLoading = ref(false);

  // 查询分会列表
  const branchList = ref([]);
  async function queryBranchList() {
    try {
      const result = await getAllBranch();
      if (result.code === 0 && result.data) {
        branchList.value = result.data;
      } else {
        branchList.value = [];
      }
    } catch (err) {
      console.error('获取分会列表失败:', err);
      smartSentry.captureError(err);
      branchList.value = [];
    }
  }

  // 查询内容分类树
  const categoryTreeData = ref([]);
  async function queryCategoryTree() {
    try {
      const result = await getContentCategoryTree({});
      if (result.code === 0 && result.data) {
        categoryTreeData.value = result.data;
      } else {
        categoryTreeData.value = [];
      }
    } catch (err) {
      console.error('获取内容分类树失败:', err);
      smartSentry.captureError(err);
      categoryTreeData.value = [];
    }
  }

  onMounted(() => {
    queryContentTypeList();
    queryBranchList();
    queryCategoryTree();
    queryContentList();
  });

  // 查询列表
  async function queryContentList() {
    try {
      tableLoading.value = true;
      const result = await contentApi.queryContent(queryForm);
      tableData.value = result.data.list;
      total.value = result.data.total;
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      tableLoading.value = false;
    }
  }

  // 点击查询
  function onSearch() {
    queryForm.pageNum = 1;
    queryContentList();
  }

  // 点击重置
  function onReload() {
    Object.assign(queryForm, queryFormState);
    publishDate.value = [];
    createDate.value = [];
    queryForm.branchId = undefined;
    queryForm.categoryId = undefined;
    queryContentList();
  }

  // 发布日期选择
  const publishDate = ref([]);
  function publishDateChange(dates, dateStrings) {
    queryForm.publishTimeBegin = dateStrings[0];
    queryForm.publishTimeEnd = dateStrings[1];
  }
  // 创建日期选择
  const createDate = ref([]);
  function createDateChange(dates, dateStrings) {
    queryForm.createTimeBegin = dateStrings[0];
    queryForm.createTimeEnd = dateStrings[1];
  }

  // ------------------ 新建、编辑 ------------------

  // 新建、编辑
  const contentFormDrawer = ref();
  function addOrUpdate(contentId) {
    if (contentFormDrawer.value) {
      contentFormDrawer.value.showModal(contentId);
    } else {
      message.error('组件未初始化完成，请稍后再试');
    }
  }

  // ------------------ 删除 ------------------

  // 删除
  function onDelete(contentId) {
    Modal.confirm({
      title: '提示',
      content: '确认删除此数据吗?',
      onOk() {
        deleteContent(contentId);
      },
    });
  }

  // 删除API
  async function deleteContent(contentId) {
    try {
      tableLoading.value = true;
      await contentApi.deleteContent(contentId);
      message.success('删除成功');
      queryContentList();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      tableLoading.value = false;
    }
  }

  // ------------------ 详情 ------------------

  // 进入详情
  const router = useRouter();
  function toDetail(contentId) {
    router.push({
      path: '/website/content/content-detail',
      query: { contentId },
    });
  }
</script>

<style lang="less" scoped></style>

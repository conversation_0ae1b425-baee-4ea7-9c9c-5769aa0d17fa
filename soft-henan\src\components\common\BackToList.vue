<template>
  <div class="back-to-list">
    <a-button @click="goBack" class="back-button" type="primary">
      <span class="back-icon">←</span>
      点击返回列表
    </a-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  // 获取当前路由路径
  const currentPath = router.currentRoute.value.path
  
  // 根据当前路径构建返回路径
  // 例如：/news/detail/123 -> /news
  // 例如：/activity/detail/456 -> /activity
  const pathSegments = currentPath.split('/')
  
  // 移除 'detail' 和 ID 部分
  const filteredSegments = pathSegments.filter((segment, index) => {
    // 保留第一个空字符串（根路径）
    if (index === 0) return true
    // 保留第一个非空段（如 news, activity 等）
    if (index === 1) return true
    // 跳过 'detail' 和 ID
    if (segment === 'detail') return false
    // 如果前一个是 'detail'，则当前是 ID，也跳过
    if (pathSegments[index - 1] === 'detail') return false
    return true
  })
  
  const backPath = filteredSegments.join('/')
  
  // 如果返回路径为空或只有根路径，则返回首页
  if (!backPath || backPath === '/') {
    router.push('/')
  } else {
    router.push(backPath)
  }
}
</script>

<style scoped>
.back-to-list {
  margin: 20px 0;
  padding: 0 20px;
}

.back-button {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  border-radius: 4px;
  height: 32px;
  padding: 0 15px;
}

.back-icon {
  font-size: 16px;
  font-weight: bold;
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .back-to-list {
    padding: 0 10px;
  }

  .back-button {
    font-size: 13px;
    height: 28px;
    padding: 0 12px;
  }
}
</style>

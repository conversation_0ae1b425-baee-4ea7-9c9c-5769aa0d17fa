package net.lingyue.ly.admin.module.website.config.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 网站配置更新表单
 */
@Data
public class WebsiteConfigUpdateForm {

    @Schema(description = "配置ID")
    @NotNull(message = "配置ID不能为空")
    private Long configId;

    @Schema(description = "配置键")
    @NotBlank(message = "配置键不能为空")
    @Size(max = 255, message = "配置键最多255个字符")
    private String configKey;

    @Schema(description = "配置值")
    @NotBlank(message = "配置值不能为空")
    @Size(max = 60000, message = "配置值最多60000个字符")
    private String configValue;

    @Schema(description = "配置名称")
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 255, message = "配置名称最多255个字符")
    private String configName;

    @Schema(description = "备注")
    @Size(max = 255, message = "备注最多255个字符")
    private String remark;
}

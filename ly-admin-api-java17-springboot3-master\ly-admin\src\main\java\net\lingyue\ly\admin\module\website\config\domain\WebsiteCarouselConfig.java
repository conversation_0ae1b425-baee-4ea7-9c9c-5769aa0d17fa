package net.lingyue.ly.admin.module.website.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 网站轮播图配置
 */
@Data
public class WebsiteCarouselConfig {

    @Schema(description = "轮播图列表")
    private List<CarouselItem> carouselItems;

    /**
     * 轮播图项
     */
    @Data
    public static class CarouselItem {

        @Schema(description = "标题")
        private String title;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "链接")
        private String link;

        @Schema(description = "排序")
        private Integer sort;

        @Schema(description = "是否启用")
        private Boolean enabled;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.lingyue.ly.admin.module.website.content.dao.ContentDao">

    <select id="queryPage" resultType="net.lingyue.ly.admin.module.website.content.domain.vo.ContentVO">
        SELECT
            c.content_id,
            c.content_type_id,
            ct.content_type_name,
            c.branch_id,
            c.title,
            c.summary,
            c.cover_image,
            c.top_flag,
            c.recommend_flag,
            c.scheduled_publish_flag,
            c.publish_time,
            c.page_view_count,
            c.source,
            c.author,
            c.status,
            c.deleted_flag,
            c.create_user_id,
            e.actual_name AS create_user_name,
            c.create_time,
            c.update_time
        FROM
            t_website_content c
        LEFT JOIN
            t_website_content_type ct ON c.content_type_id = ct.content_type_id
        LEFT JOIN
            t_employee e ON c.create_user_id = e.employee_id
        <where>
            <if test="queryForm.contentTypeId != null">
                AND c.content_type_id = #{queryForm.contentTypeId}
            </if>
            <if test="queryForm.branchId != null">
                AND c.branch_id = #{queryForm.branchId}
            </if>
            <if test="queryForm.categoryId != null">
                AND c.content_id IN (
                    SELECT content_id FROM t_website_content_category_relation WHERE category_id = #{queryForm.categoryId}
                )
            </if>
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                    c.title LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR c.author LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR c.source LIKE CONCAT('%', #{queryForm.keywords}, '%')
                )
            </if>
            <if test="queryForm.createUserId != null">
                AND c.create_user_id = #{queryForm.createUserId}
            </if>
            <if test="queryForm.deletedFlag != null">
                AND c.deleted_flag = #{queryForm.deletedFlag}
            </if>
            <if test="queryForm.status != null">
                AND c.status = #{queryForm.status}
            </if>
            <if test="queryForm.topFlag != null">
                AND c.top_flag = #{queryForm.topFlag}
            </if>
            <if test="queryForm.recommendFlag != null">
                AND c.recommend_flag = #{queryForm.recommendFlag}
            </if>
            <if test="queryForm.publishTimeBegin != null and queryForm.publishTimeBegin != ''">
                AND c.publish_time &gt;= #{queryForm.publishTimeBegin}
            </if>
            <if test="queryForm.publishTimeEnd != null and queryForm.publishTimeEnd != ''">
                AND c.publish_time &lt;= #{queryForm.publishTimeEnd}
            </if>
            <if test="queryForm.createTimeBegin != null and queryForm.createTimeBegin != ''">
                AND c.create_time &gt;= #{queryForm.createTimeBegin}
            </if>
            <if test="queryForm.createTimeEnd != null and queryForm.createTimeEnd != ''">
                AND c.create_time &lt;= #{queryForm.createTimeEnd}
            </if>
        </where>
        ORDER BY
            c.top_flag DESC,
            c.update_time DESC
    </select>

</mapper>

/*
 * 网站配置API
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const websiteConfigApi = {
  /**
   * 获取网站基本信息配置
   */
  getWebsiteBasicConfig: () => {
    return getRequest('/website/config/basic');
  },

  /**
   * 更新网站基本信息配置
   */
  updateWebsiteBasicConfig: (param) => {
    return postRequest('/website/config/basic/update', param);
  },

  /**
   * 获取网站轮播图配置
   */
  getWebsiteCarouselConfig: () => {
    return getRequest('/website/config/carousel');
  },

  /**
   * 更新网站轮播图配置
   */
  updateWebsiteCarouselConfig: (param) => {
    console.log('API调用 - 更新网站轮播图配置:', param);
    return postRequest('/website/config/carousel/update', param).then(res => {
      console.log('API响应 - 更新网站轮播图配置:', res);
      return res;
    });
  },

  /**
   * 获取网站底部信息配置
   */
  getWebsiteFooterConfig: () => {
    return getRequest('/website/config/footer');
  },

  /**
   * 更新网站底部信息配置
   */
  updateWebsiteFooterConfig: (param) => {
    return postRequest('/website/config/footer/update', param);
  },

  /**
   * 获取网站主题颜色配置
   */
  getWebsiteThemeConfig: () => {
    return getRequest('/website/config/theme');
  },

  /**
   * 更新网站主题颜色配置
   */
  updateWebsiteThemeConfig: (param) => {
    console.log('API调用 - 更新网站主题颜色配置:', param);
    return postRequest('/website/config/theme/update', param).then(res => {
      console.log('API响应 - 更新网站主题颜色配置:', res);
      return res;
    });
  },
};

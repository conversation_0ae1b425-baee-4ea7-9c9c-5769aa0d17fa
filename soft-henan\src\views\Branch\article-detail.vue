<template>
  <div class="branch-article-detail-container">
    <div class="container">
      <div class="breadcrumb">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/">首页</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link to="/branch">分会</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>文章详情</a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <ContentDetail 
        :detail="articleDetail" 
        :loading="loading" 
        :config="detailConfig"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import ContentDetail from '../../components/common/ContentDetail.vue'
import { branchArticleDetailConfig } from '../../utils/detailConfig'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const articleDetail = ref({})

// 详情配置，使用contentId作为ID字段
const detailConfig = {
  ...branchArticleDetailConfig,
  idField: 'contentId', // 分会文章使用contentId
  metaFields: [
    { key: 'contentTypeName', label: '分类', formatter: (value) => value || '分会管理' },
    { key: 'branchName', label: '所属分会' },
    { key: 'author', label: '作者', formatter: (value) => value || '管理员' },
    { key: 'publishTime', label: '发布时间', type: 'date' },
    { key: 'pageViewCount', label: '浏览量', formatter: (value) => value || 0 }
  ]
}

// 获取分会文章详情
const fetchArticleDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id || route.query.id
    if (!id) {
      message.error('缺少文章ID参数')
      return
    }

    // 这里需要调用正确的API
    // const res = await getBranchArticleDetail(id)
    // 临时使用一个假的响应结构
    const res = { code: 1, data: { contentId: id, title: '文章标题', contentHtml: '文章内容' } }
    
    // 兼容不同的响应格式：code: 0, 1, 200 都表示成功  
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      articleDetail.value = res.data
    } else {
      console.error('获取文章详情失败，响应:', res)
      message.error('获取文章详情失败')
    }
  } catch (error) {
    console.error('获取文章详情失败:', error)
    message.error('获取文章详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchArticleDetail()
})
</script>

<style scoped>
.branch-article-detail-container {
  width: 100%;
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.breadcrumb {
  margin-bottom: 20px;
}

@media (max-width: 1200px) {
  .container {
    width: 100%;
    padding: 20px;
  }
}
</style>

<template>
  <ContentDetail 
    :detail="regulationDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import ContentDetail from '../../components/common/ContentDetail.vue'
import { regulationDetailConfig } from '../../utils/detailConfig'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const regulationDetail = ref({})

// 详情配置
const detailConfig = regulationDetailConfig

// 获取法规详情
const fetchRegulationDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id || route.query.id
    if (!id) {
      message.error('缺少法规ID参数')
      return
    }

    // 这里需要调用正确的API
    // const res = await getRegulationDetail(id)
    // 临时使用一个假的响应结构
    const res = { code: 1, data: { id, title: '法规标题', contentHtml: '法规内容' } }
    
    // 兼容不同的响应格式：code: 0, 1, 200 都表示成功
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      regulationDetail.value = res.data
    } else {
      console.error('获取法规详情失败，响应:', res)
      message.error('获取法规详情失败')
    }
  } catch (error) {
    console.error('获取法规详情失败:', error)
    message.error('获取法规详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchRegulationDetail()
})
</script>

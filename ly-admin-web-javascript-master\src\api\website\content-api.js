/*
 * 内容管理 api
 */
import { postRequest, getRequest, request } from '/@/lib/axios';

export const contentApi = {
  // -------------------- 内容类型 --------------------
  // 获取所有内容类型
  getAllContentTypes: () => {
    return getRequest('/website/contentType/getAll');
  },

  // 添加内容类型（使用表单格式）
  addContentType: (contentTypeName, contentTypeCode, contentTypeDesc, sort) => {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('contentTypeName', contentTypeName);
    formData.append('contentTypeCode', contentTypeCode);
    if (contentTypeDesc) formData.append('contentTypeDesc', contentTypeDesc);
    if (sort !== undefined) formData.append('sort', sort);

    // 使用表单格式发送请求
    return request({
      url: '/website/contentType/add',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // 更新内容类型（使用表单格式）
  updateContentType: (contentTypeId, contentTypeName, contentTypeDesc, sort) => {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('contentTypeId', contentTypeId);
    formData.append('contentTypeName', contentTypeName);
    if (contentTypeDesc) formData.append('contentTypeDesc', contentTypeDesc);
    if (sort !== undefined) formData.append('sort', sort);

    // 使用表单格式发送请求
    return request({
      url: '/website/contentType/update',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // 更新内容类型状态
  updateContentTypeStatus: (contentTypeId, enableFlag) => {
    return getRequest('/website/contentType/updateStatus', { contentTypeId, enableFlag });
  },

  // 删除内容类型
  deleteContentType: (contentTypeId) => {
    return getRequest(`/website/contentType/delete/${contentTypeId}`);
  },

  // -------------------- 内容管理 --------------------
  // 分页查询内容
  queryContent: (queryForm) => {
    return postRequest('/website/content/query', queryForm);
  },

  // 添加内容
  addContent: (addForm) => {
    return postRequest('/website/content/add', addForm);
  },

  // 更新内容
  updateContent: (updateForm) => {
    return postRequest('/website/content/update', updateForm);
  },

  // 获取内容更新表单
  getUpdateContentInfo: (contentId) => {
    return getRequest(`/website/content/getUpdateVO/${contentId}`);
  },

  // 获取内容详情
  getContentDetail: (contentId) => {
    return getRequest(`/website/content/getDetail/${contentId}`);
  },

  // 删除内容
  deleteContent: (contentId) => {
    return getRequest(`/website/content/delete/${contentId}`);
  },
};

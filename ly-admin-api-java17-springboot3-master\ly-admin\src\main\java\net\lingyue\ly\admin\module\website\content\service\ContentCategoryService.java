package net.lingyue.ly.admin.module.website.content.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.module.website.content.dao.ContentCategoryDao;
import net.lingyue.ly.admin.module.website.content.dao.ContentCategoryRelationDao;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentCategoryEntity;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryAddForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryUpdateForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentCategoryTreeVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentCategoryVO;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容分类服务
 */
@Slf4j
@Service
public class ContentCategoryService {

    @Resource
    private ContentCategoryDao contentCategoryDao;

    @Resource
    private ContentCategoryRelationDao contentCategoryRelationDao;

    /**
     * 添加内容分类
     *
     * @param addForm 添加表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ContentCategoryAddForm addForm) {
        // 检查同级下是否有相同名称的分类
        Long parentId = addForm.getParentId() == null ? 0L : addForm.getParentId();
        LambdaQueryWrapper<ContentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentCategoryEntity::getParentId, parentId)
                .eq(ContentCategoryEntity::getCategoryName, addForm.getCategoryName())
                .eq(ContentCategoryEntity::getDeletedFlag, false);
        if (contentCategoryDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("同级下已存在相同名称的分类");
        }

        // 创建分类实体
        ContentCategoryEntity categoryEntity = SmartBeanUtil.copy(addForm, ContentCategoryEntity.class);
        categoryEntity.setParentId(parentId);
        categoryEntity.setDisabledFlag(false);
        categoryEntity.setDeletedFlag(false);
        categoryEntity.setSort(addForm.getSort() == null ? 0 : addForm.getSort());

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        categoryEntity.setCreateTime(now);
        categoryEntity.setUpdateTime(now);

        contentCategoryDao.insert(categoryEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新内容分类
     *
     * @param updateForm 更新表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ContentCategoryUpdateForm updateForm) {
        ContentCategoryEntity categoryEntity = contentCategoryDao.selectById(updateForm.getCategoryId());
        if (categoryEntity == null) {
            return ResponseDTO.userErrorParam("分类不存在");
        }

        // 检查同级下是否有相同名称的分类
        Long parentId = updateForm.getParentId() == null ? 0L : updateForm.getParentId();
        if (!categoryEntity.getCategoryName().equals(updateForm.getCategoryName()) || !categoryEntity.getParentId().equals(parentId)) {
            LambdaQueryWrapper<ContentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ContentCategoryEntity::getParentId, parentId)
                    .eq(ContentCategoryEntity::getCategoryName, updateForm.getCategoryName())
                    .eq(ContentCategoryEntity::getDeletedFlag, false)
                    .ne(ContentCategoryEntity::getCategoryId, updateForm.getCategoryId());
            if (contentCategoryDao.selectCount(queryWrapper) > 0) {
                return ResponseDTO.userErrorParam("同级下已存在相同名称的分类");
            }
        }

        // 更新分类实体
        categoryEntity.setCategoryName(updateForm.getCategoryName());
        categoryEntity.setParentId(parentId);
        categoryEntity.setSort(updateForm.getSort() == null ? 0 : updateForm.getSort());
        categoryEntity.setRemark(updateForm.getRemark());
        if (updateForm.getDisabledFlag() != null) {
            categoryEntity.setDisabledFlag(updateForm.getDisabledFlag());
        }
        categoryEntity.setUpdateTime(LocalDateTime.now());

        contentCategoryDao.updateById(categoryEntity);
        return ResponseDTO.ok();
    }

    /**
     * 删除内容分类
     *
     * @param categoryId 分类ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long categoryId) {
        ContentCategoryEntity categoryEntity = contentCategoryDao.selectById(categoryId);
        if (categoryEntity == null) {
            return ResponseDTO.userErrorParam("分类不存在");
        }

        // 检查是否有子分类
        LambdaQueryWrapper<ContentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentCategoryEntity::getParentId, categoryId)
                .eq(ContentCategoryEntity::getDeletedFlag, false);
        if (contentCategoryDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("该分类下有子分类，无法删除");
        }

        // 检查是否有内容使用该分类
        List<Long> contentIds = contentCategoryRelationDao.selectContentIdsByCategoryId(categoryId);
        if (contentIds != null && !contentIds.isEmpty()) {
            return ResponseDTO.userErrorParam("该分类下有内容，无法删除");
        }

        // 逻辑删除
        categoryEntity.setDeletedFlag(true);
        categoryEntity.setUpdateTime(LocalDateTime.now());
        contentCategoryDao.updateById(categoryEntity);

        return ResponseDTO.ok();
    }

    /**
     * 获取分类详情
     *
     * @param categoryId 分类ID
     * @return 分类详情
     */
    public ResponseDTO<ContentCategoryVO> getDetail(Long categoryId) {
        ContentCategoryEntity categoryEntity = contentCategoryDao.selectById(categoryId);
        if (categoryEntity == null || categoryEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("分类不存在");
        }

        ContentCategoryVO categoryVO = SmartBeanUtil.copy(categoryEntity, ContentCategoryVO.class);
        return ResponseDTO.ok(categoryVO);
    }

    /**
     * 查询分类树
     *
     * @param queryForm 查询表单
     * @return 分类树
     */
    public ResponseDTO<List<ContentCategoryTreeVO>> queryTree(ContentCategoryQueryForm queryForm) {
        Long parentId = queryForm.getParentId() == null ? 0L : queryForm.getParentId();
        
        // 查询所有分类
        LambdaQueryWrapper<ContentCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentCategoryEntity::getDeletedFlag, false);
        if (queryForm.getIncludeDisabled() == null || !queryForm.getIncludeDisabled()) {
            queryWrapper.eq(ContentCategoryEntity::getDisabledFlag, false);
        }
        queryWrapper.orderByAsc(ContentCategoryEntity::getSort);
        List<ContentCategoryEntity> allCategories = contentCategoryDao.selectList(queryWrapper);
        
        // 构建树形结构
        List<ContentCategoryTreeVO> treeList = buildCategoryTree(allCategories, parentId);
        
        return ResponseDTO.ok(treeList);
    }

    /**
     * 构建分类树
     *
     * @param allCategories 所有分类
     * @param parentId 父分类ID
     * @return 分类树
     */
    private List<ContentCategoryTreeVO> buildCategoryTree(List<ContentCategoryEntity> allCategories, Long parentId) {
        // 过滤出当前层级的分类
        List<ContentCategoryEntity> currentLevelCategories = allCategories.stream()
                .filter(category -> category.getParentId().equals(parentId))
                .collect(Collectors.toList());
        
        // 转换为树形VO
        List<ContentCategoryTreeVO> treeList = new ArrayList<>();
        for (ContentCategoryEntity category : currentLevelCategories) {
            ContentCategoryTreeVO treeVO = SmartBeanUtil.copy(category, ContentCategoryTreeVO.class);
            treeVO.setValue(category.getCategoryId());
            treeVO.setLabel(category.getCategoryName());
            
            // 递归设置子分类
            List<ContentCategoryTreeVO> children = buildCategoryTree(allCategories, category.getCategoryId());
            treeVO.setChildren(children);
            
            treeList.add(treeVO);
        }
        
        return treeList;
    }
}

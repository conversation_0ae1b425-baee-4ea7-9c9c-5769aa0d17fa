package net.lingyue.ly.admin.module.website.content.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 网站内容实体
 */
@Data
@TableName("t_website_content")
public class ContentEntity {

    @TableId(type = IdType.AUTO)
    private Long contentId;

    /**
     * 内容类型ID
     */
    private Long contentTypeId;

    /**
     * 分会ID
     */
    private Long branchId;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 是否定时发布
     */
    private Boolean scheduledPublishFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 活动地点
     */
    private String activityLocation;

    /**
     * 主办单位
     */
    private String activityOrganizer;

    /**
     * 联系方式
     */
    private String activityContact;

    /**
     * 内容 纯文本
     */
    private String contentText;

    /**
     * 内容 html
     */
    private String contentHtml;

    /**
     * 附件
     * 多个英文逗号分隔
     */
    private String attachment;

    /**
     * 页面浏览量
     */
    private Integer pageViewCount;

    /**
     * 来源
     */
    private String source;

    /**
     * 作者
     */
    private String author;

    /**
     * SEO关键词
     */
    private String seoKeywords;

    /**
     * SEO描述
     */
    private String seoDescription;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    private Integer status;

    private Boolean deletedFlag;

    private Long createUserId;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}

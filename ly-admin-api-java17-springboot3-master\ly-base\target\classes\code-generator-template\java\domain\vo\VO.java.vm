package ${packageName};

#foreach ($importClass in $importPackageList)
    $importClass
#end

/**
 * ${basic.description} 列表VO
 *
 * <AUTHOR>
 * @Date ${basic.backendDate}
 * @Copyright ${basic.copyright}
 */

@Data
public class ${name.upperCamel}VO {

    #foreach ($field in $fields)

        ${field.apiModelProperty}$!{field.notEmpty}$!{field.dict}$!{field.file}
        private $field.javaType $field.fieldName;
    #end

}

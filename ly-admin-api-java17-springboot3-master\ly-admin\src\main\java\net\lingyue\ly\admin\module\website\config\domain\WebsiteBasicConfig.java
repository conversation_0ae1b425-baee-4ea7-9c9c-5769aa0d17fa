package net.lingyue.ly.admin.module.website.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站基本信息配置
 */
@Data
public class WebsiteBasicConfig {

    @Schema(description = "网站名称")
    private String websiteName;

    @Schema(description = "网站LOGO")
    private String websiteLogo;

    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "机构英文名称")
    private String organizationEnglishName;

    @Schema(description = "ICP备案号")
    private String icpNumber;

    @Schema(description = "公网安备号")
    private String publicSecurityNumber;

    @Schema(description = "版权信息")
    private String copyrightInfo;

    @Schema(description = "法律顾问信息")
    private String legalAdvisorInfo;

    @Schema(description = "地图API密钥")
    private String mapApiKey;

    @Schema(description = "地图安全密钥")
    private String mapSecurityCode;

    @Schema(description = "地图中心点经度")
    private Double mapCenterLongitude;

    @Schema(description = "地图中心点纬度")
    private Double mapCenterLatitude;

    @Schema(description = "地图缩放级别")
    private Integer mapZoom;

    @Schema(description = "地图标记点标题")
    private String mapMarkerTitle;

    @Schema(description = "地图标记点地址")
    private String mapMarkerAddress;
}

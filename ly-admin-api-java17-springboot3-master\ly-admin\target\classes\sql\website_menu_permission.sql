-- 添加前台网站菜单管理菜单项
INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, frame_flag, cache_flag, visible_flag,
                    disabled_flag, perms_type, web_perms, icon, create_user_id)
VALUES ('前台导航管理', 2, 0, '/website/menu',
        '/website/menu/website-menu-list.vue', false, false, true, false, 1, 'website:menu:query', 'MenuOutlined', 1);

-- 按菜单名称查询该菜单的 menu_id 作为按钮权限的 父菜单ID 与 功能点关联菜单ID
SET @parent_id = NULL;
SELECT t_menu.menu_id
INTO @parent_id
FROM t_menu
WHERE t_menu.menu_name = '前台导航管理';

-- 添加按钮权限
INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms,
                    web_perms, perms_type, context_menu_id, create_user_id)
VALUES ('查询', 3, @parent_id, false, true, true, false, 'website:menu:query', 'website:menu:query', 1, @parent_id, 1);

INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms,
                    web_perms, perms_type, context_menu_id, create_user_id)
VALUES ('添加', 3, @parent_id, false, true, true, false, 'website:menu:add', 'website:menu:add', 1, @parent_id, 1);

INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms,
                    web_perms, perms_type, context_menu_id, create_user_id)
VALUES ('更新', 3, @parent_id, false, true, true, false, 'website:menu:update', 'website:menu:update', 1, @parent_id, 1);

INSERT INTO t_menu (menu_name, menu_type, parent_id, frame_flag, cache_flag, visible_flag, disabled_flag, api_perms,
                    web_perms, perms_type, context_menu_id, create_user_id)
VALUES ('删除', 3, @parent_id, false, true, true, false, 'website:menu:batchDelete', 'website:menu:batchDelete', 1, @parent_id, 1);

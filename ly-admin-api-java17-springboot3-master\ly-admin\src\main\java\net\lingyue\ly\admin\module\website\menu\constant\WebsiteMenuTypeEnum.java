package net.lingyue.ly.admin.module.website.menu.constant;

import net.lingyue.ly.base.common.enumeration.BaseEnum;

/**
 * 前台网站菜单类型枚举
 */
public enum WebsiteMenuTypeEnum implements BaseEnum {
    /**
     * 目录
     */
    CATALOG(1, "目录"),
    /**
     * 菜单
     */
    MENU(2, "菜单");

    private final Integer value;

    private final String desc;

    WebsiteMenuTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}

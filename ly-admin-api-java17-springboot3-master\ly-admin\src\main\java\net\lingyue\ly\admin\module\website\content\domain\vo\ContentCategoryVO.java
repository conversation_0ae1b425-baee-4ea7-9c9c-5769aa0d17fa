package net.lingyue.ly.admin.module.website.content.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容分类VO
 */
@Data
public class ContentCategoryVO {

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "父分类ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否禁用")
    private Boolean disabledFlag;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}

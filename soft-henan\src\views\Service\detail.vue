<template>
  <ContentDetail 
    :detail="serviceDetail" 
    :loading="loading" 
    :config="detailConfig"
  />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'
import ContentDetail from '../../components/common/ContentDetail.vue'

const route = useRoute()
const loading = ref(false)
const serviceDetail = ref({})

// 详情配置
const detailConfig = computed(() => ({
  // 基础字段配置
  idField: 'id',
  titleField: 'title',
  summaryField: 'summary',
  contentField: 'contentHtml',
  attachmentField: 'attachments',
  
  // 元数据字段（顶部显示）
  metaFields: [
    { key: 'serviceType', label: '服务类型' },
    { key: 'serviceTime', label: '服务时间' },
    { key: 'serviceLocation', label: '服务地点' },
    { key: 'contact', label: '联系方式' }
  ],
  
  // 信息区域配置（左侧蓝色边框区域）
  infoSection: {
    fields: [
      { key: 'serviceType', label: '服务类型' },
      { key: 'serviceTime', label: '服务时间' },
      { key: 'serviceLocation', label: '服务地点' },
      { key: 'contact', label: '联系方式' },
      { key: 'serviceFee', label: '服务费用' },
      { key: 'serviceHours', label: '服务时间' }
    ]
  },
  
  // 摘要配置
  summaryTitle: '服务简介',
  
  // 其他配置
  showActions: false, // 不显示底部的返回按钮，因为已经有BackToList组件了
  emptyText: '未找到服务信息',
  containerClass: 'service-detail-container'
}))

// 获取服务详情
const fetchServiceDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    if (!id) {
      message.error('缺少服务ID参数')
      return
    }

    const res = await getContentDetail(id)
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      // 转换数据格式以适配ContentDetail组件
      serviceDetail.value = {
        id: res.data.contentId,
        title: res.data.title,
        serviceType: res.data.serviceType || '咨询服务',
        serviceTime: formatDate(res.data.publishTime || res.data.createTime),
        serviceLocation: res.data.serviceLocation || '待定',
        contact: res.data.contact || '0371-12345678',
        serviceFee: res.data.serviceFee || '免费',
        serviceHours: res.data.serviceHours || '周一至周五 9:00-17:00',
        summary: res.data.summary,
        contentHtml: res.data.contentHtml || res.data.description,
        attachments: res.data.attachments || []
      }
    } else {
      message.error('获取服务详情失败')
    }
  } catch (error) {
    console.error('获取服务详情失败:', error)
    message.error('获取服务详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面加载时获取服务详情
onMounted(() => {
  fetchServiceDetail()
})
</script>

<style scoped>
.service-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
}
</style>

<template>
  <div class="regulation-container">
    <div class="container">
      <h1 class="page-title">管理制度</h1>
      <div class="regulation-content">
        <a-spin :spinning="loading" tip="加载中...">
          <div class="min-height-container">
            <div class="regulation-list" v-if="regulationList.length > 0">
              <div class="regulation-item" v-for="(item, index) in regulationList" :key="index">
                <div class="regulation-image">
                  <img v-if="item.image" :src="item.image" :alt="item.title" />
                </div>
                <div class="regulation-info">
                  <div class="regulation-date">{{ formatDate(item.publishTime) }}</div>
                  <h3 class="regulation-title">{{ item.title }}</h3>
                  <p class="regulation-desc">{{ item.summary || item.content || '暂无描述' }}</p>
                  <router-link :to="{ path: '/regulation-detail', query: { id: item.id } }" class="read-more">查看详情</router-link>
                </div>
              </div>
            </div>
            <div class="empty-data" v-else>
              <a-empty description="暂无数据" />
            </div>

            <div class="pagination">
              <a-pagination
                v-model:current="pagination.current"
                :total="pagination.total"
                :pageSize="pagination.pageSize"
                @change="handlePageChange"
                showSizeChanger
                :pageSizeOptions="['5', '10', '20']"
                @showSizeChange="handleSizeChange"
              />
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const regulationList = ref([])

// 页面加载
onMounted(() => {
  console.log('规章制度页面已加载')
  initRegulationData()
  console.log('规章制度数据初始化完成，列表长度:', regulationList.value.length)
})

// 初始化规章制度数据
const initRegulationData = () => {
  regulationList.value = [
    {
      id: 1,
      title: '河南省软组织病研究会章程',
      summary: '本章程规定了研究会的宗旨、任务、组织机构、会员权利义务等基本事项，是研究会开展各项工作的根本依据。',
      content: '第一章 总则\n第一条 本会名称为河南省软组织病研究会...',
      publishTime: '2024-01-01',
      category: '基本制度',
      author: '河南省软组织病研究会'
    },
    {
      id: 2,
      title: '会员管理办法',
      summary: '为规范会员的入会、退会、权利义务等管理工作，建立健全会员管理制度，促进研究会健康发展。',
      content: '第一条 为规范会员管理，根据本会章程制定本办法...',
      publishTime: '2024-01-02',
      category: '会员管理',
      author: '河南省软组织病研究会'
    },
    {
      id: 3,
      title: '学术活动管理规定',
      summary: '为加强学术活动的组织管理，提高学术活动质量，规范学术行为，促进学术交流与合作。',
      content: '第一条 为加强学术活动管理，提高学术质量...',
      publishTime: '2024-01-03',
      category: '学术管理',
      author: '河南省软组织病研究会'
    },
    {
      id: 4,
      title: '财务管理制度',
      summary: '为规范财务管理工作，确保资金使用合规合理，提高资金使用效益，维护研究会财务安全。',
      content: '第一条 为规范财务管理，确保资金使用合规...',
      publishTime: '2024-01-04',
      category: '财务管理',
      author: '河南省软组织病研究会'
    },
    {
      id: 5,
      title: '档案管理制度',
      summary: '为加强档案管理工作，规范档案收集、整理、保管、利用等各个环节，确保档案资料的完整性和安全性。',
      content: '第一条 为加强档案管理，规范档案工作...',
      publishTime: '2024-01-05',
      category: '档案管理',
      author: '河南省软组织病研究会'
    },
    {
      id: 6,
      title: '印章使用管理办法',
      summary: '为规范印章的使用和管理，确保印章使用的合法性和安全性，维护研究会的合法权益。',
      content: '第一条 为规范印章使用管理，制定本办法...',
      publishTime: '2024-01-06',
      category: '行政管理',
      author: '河南省软组织病研究会'
    }
  ]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #d32f2f;
}

.min-height-container {
  min-height: 400px;
}

.regulation-content {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

.regulation-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.regulation-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  padding: 20px;
}

.regulation-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.regulation-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.regulation-date {
  color: #999;
  font-size: 12px;
  margin-bottom: 8px;
}

.regulation-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.regulation-desc {
  color: #777;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.read-more {
  color: #d32f2f;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: #b71c1c;
  text-decoration: underline;
}

.empty-data {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 100%;
    padding: 15px;
  }
  
  .regulation-item {
    flex-direction: column;
    padding: 15px;
  }
}
</style>
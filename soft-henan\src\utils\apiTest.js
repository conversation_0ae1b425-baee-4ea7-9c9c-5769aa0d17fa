import api from '../api/index'

/**
 * 测试后端API连接
 */
export const testApiConnection = async () => {
  try {
    console.log('开始测试API连接...')
    
    // 测试菜单API
    const menuResponse = await api.get('/website/menu/front')
    console.log('菜单API响应:', menuResponse)
    
    if (menuResponse && menuResponse.data) {
      console.log('✅ 菜单API连接成功，返回数据:', menuResponse.data)
      return {
        success: true,
        data: menuResponse.data,
        message: 'API连接成功'
      }
    } else {
      console.log('⚠️ 菜单API返回空数据')
      return {
        success: false,
        data: null,
        message: 'API返回空数据'
      }
    }
  } catch (error) {
    console.error('❌ API连接失败:', error)
    return {
      success: false,
      data: null,
      message: `API连接失败: ${error.message}`
    }
  }
}

/**
 * 测试基础API连接
 */
export const testBasicApi = async () => {
  try {
    console.log('测试基础API连接...')
    const response = await api.get('/')
    console.log('基础API响应:', response)
    return {
      success: true,
      data: response,
      message: '基础API连接成功'
    }
  } catch (error) {
    console.error('基础API连接失败:', error)
    return {
      success: false,
      data: null,
      message: `基础API连接失败: ${error.message}`
    }
  }
}

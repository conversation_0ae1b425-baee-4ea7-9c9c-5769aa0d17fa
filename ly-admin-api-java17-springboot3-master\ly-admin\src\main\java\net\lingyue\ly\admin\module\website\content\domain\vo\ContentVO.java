package net.lingyue.ly.admin.module.website.content.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容VO
 */
@Data
public class ContentVO {

    private Long contentId;

    /**
     * 内容类型ID
     */
    private Long contentTypeId;

    /**
     * 内容类型名称
     */
    private String contentTypeName;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 是否已发布
     */
    private Boolean publishFlag;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 活动地点
     */
    private String activityLocation;

    /**
     * 主办单位
     */
    private String activityOrganizer;

    /**
     * 联系方式
     */
    private String activityContact;

    /**
     * 页面浏览量
     */
    private Integer pageViewCount;

    /**
     * 来源
     */
    private String source;

    /**
     * 作者
     */
    private String author;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 是否删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

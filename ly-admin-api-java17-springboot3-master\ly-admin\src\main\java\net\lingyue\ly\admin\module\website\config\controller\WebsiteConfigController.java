package net.lingyue.ly.admin.module.website.config.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteBasicConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteCarouselConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteFooterConfig;
import net.lingyue.ly.admin.module.website.config.domain.WebsiteThemeConfig;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigAddForm;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigQueryForm;
import net.lingyue.ly.admin.module.website.config.domain.form.WebsiteConfigUpdateForm;
import net.lingyue.ly.admin.module.website.config.domain.vo.WebsiteConfigVO;
import net.lingyue.ly.admin.module.website.config.service.WebsiteConfigService;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

/**
 * 网站配置控制器
 */
@RestController
@Tag(name = AdminSwaggerTagConst.Website.WEBSITE_CONFIG)
public class WebsiteConfigController {

    @Resource
    private WebsiteConfigService websiteConfigService;

    @Operation(summary = "分页查询网站配置")
    @PostMapping("/website/config/query")
    @SaCheckPermission("website:config:query")
    public ResponseDTO<PageResult<WebsiteConfigVO>> queryConfigPage(@RequestBody @Valid WebsiteConfigQueryForm queryForm) {
        return websiteConfigService.queryConfigPage(queryForm);
    }

    @Operation(summary = "添加网站配置")
    @PostMapping("/website/config/add")
    @SaCheckPermission("website:config:add")
    public ResponseDTO<String> addConfig(@RequestBody @Valid WebsiteConfigAddForm configAddForm) {
        return websiteConfigService.add(configAddForm);
    }

    @Operation(summary = "修改网站配置")
    @PostMapping("/website/config/update")
    @SaCheckPermission("website:config:update")
    public ResponseDTO<String> updateConfig(@RequestBody @Valid WebsiteConfigUpdateForm updateForm) {
        return websiteConfigService.updateConfig(updateForm);
    }

    @Operation(summary = "获取网站基本信息配置")
    @GetMapping("/website/config/basic")
    @SaCheckPermission("website:config:query")
    public ResponseDTO<WebsiteBasicConfig> getWebsiteBasicConfig() {
        return websiteConfigService.getWebsiteBasicConfig();
    }

    @Operation(summary = "更新网站基本信息配置")
    @PostMapping("/website/config/basic/update")
    @SaCheckPermission("website:config:update")
    public ResponseDTO<String> updateWebsiteBasicConfig(@RequestBody @Valid WebsiteBasicConfig config) {
        return websiteConfigService.updateWebsiteBasicConfig(config);
    }

    @Operation(summary = "获取网站轮播图配置")
    @GetMapping("/website/config/carousel")
    @SaCheckPermission("website:config:query")
    public ResponseDTO<WebsiteCarouselConfig> getWebsiteCarouselConfig() {
        return websiteConfigService.getWebsiteCarouselConfig();
    }

    @Operation(summary = "更新网站轮播图配置")
    @PostMapping("/website/config/carousel/update")
    @SaCheckPermission("website:config:update")
    public ResponseDTO<String> updateWebsiteCarouselConfig(@RequestBody @Valid WebsiteCarouselConfig config) {
        return websiteConfigService.updateWebsiteCarouselConfig(config);
    }

    @Operation(summary = "获取网站底部信息配置")
    @GetMapping("/website/config/footer")
    @SaCheckPermission("website:config:query")
    public ResponseDTO<WebsiteFooterConfig> getWebsiteFooterConfig() {
        return websiteConfigService.getWebsiteFooterConfig();
    }

    @Operation(summary = "更新网站底部信息配置")
    @PostMapping("/website/config/footer/update")
    @SaCheckPermission("website:config:update")
    public ResponseDTO<String> updateWebsiteFooterConfig(@RequestBody @Valid WebsiteFooterConfig config) {
        return websiteConfigService.updateWebsiteFooterConfig(config);
    }

    @Operation(summary = "获取网站主题颜色配置")
    @GetMapping("/website/config/theme")
    @SaCheckPermission("website:config:query")
    public ResponseDTO<WebsiteThemeConfig> getWebsiteThemeConfig() {
        return websiteConfigService.getWebsiteThemeConfig();
    }

    @Operation(summary = "更新网站主题颜色配置")
    @PostMapping("/website/config/theme/update")
    @SaCheckPermission("website:config:update")
    public ResponseDTO<String> updateWebsiteThemeConfig(@RequestBody @Valid WebsiteThemeConfig config) {
        return websiteConfigService.updateWebsiteThemeConfig(config);
    }
}

<template>
  <div :class="containerClass">
    <div class="container">
      <!-- 返回列表按钮 -->
      <div class="back-to-list-section">
        <BackToList />
      </div>
      
      <div class="detail-wrapper">
        <a-spin :spinning="loading" tip="加载中...">
          <!-- 详情内容 -->
          <div class="detail-content" v-if="detail && detail[config.idField]">
            <!-- 标题 -->
            <h1 class="detail-title">{{ detail[config.titleField] }}</h1>
            
            <!-- 元数据信息 -->
            <div class="detail-meta" v-if="config.metaFields && config.metaFields.length > 0">
              <template v-for="(field, index) in config.metaFields" :key="field?.key || index">
                <span v-if="field && getFieldValue(field)" class="meta-item">
                  <span v-if="field.label" class="meta-label">{{ field.label }}：</span>
                  <span class="meta-value">{{ formatFieldValue(field, getFieldValue(field)) }}</span>
                </span>
                <span v-if="index < config.metaFields.length - 1 && field && getFieldValue(field)" class="meta-divider">|</span>
              </template>
            </div>

            <!-- 特殊信息区域（如活动信息、代表处信息等） -->
            <div v-if="config.infoSection && hasInfoData()" class="info-section">
              <div class="info-table">
                <div v-for="field in config.infoSection.fields" :key="field?.key || Math.random()" class="info-row" v-if="field && getFieldValue(field)">
                  <div class="info-label">{{ field.label }}：</div>
                  <div class="info-value">{{ formatFieldValue(field, getFieldValue(field)) }}</div>
                </div>
              </div>
            </div>

            <!-- 摘要 -->
            <div v-if="config.summaryField && detail[config.summaryField]" class="detail-summary">
              <div class="summary-title">{{ config.summaryTitle || '摘要' }}：</div>
              <div class="summary-content">{{ detail[config.summaryField] }}</div>
            </div>

            <!-- 主要内容 -->
            <div v-if="config.contentField && detail[config.contentField]" class="detail-content-body" v-html="detail[config.contentField]"></div>

            <!-- 附件 -->
            <div v-if="config.attachmentField && hasAttachments()" class="detail-attachments">
              <div class="attachments-title">附件：</div>
              <div class="attachments-list">
                <div v-for="(item, index) in getAttachments()" :key="index" class="attachment-item">
                  <a :href="getFileUrl(item)" target="_blank">
                    {{ getAttachmentName(item, index) }}
                  </a>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="detail-actions" v-if="config.showActions">
              <a-button type="primary" @click="goBack">{{ config.backButtonText || '返回列表' }}</a-button>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div class="empty-data" v-else-if="!loading">
            <a-empty :description="config.emptyText || '未找到内容'" />
            <div class="empty-actions" v-if="config.showActions">
              <a-button type="primary" @click="goBack">{{ config.backButtonText || '返回列表' }}</a-button>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BackToList from './BackToList.vue'

const props = defineProps({
  // 详情数据
  detail: {
    type: Object,
    default: () => ({})
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 配置对象
  config: {
    type: Object,
    required: true
  }
})

const router = useRouter()

// 调试信息
onMounted(() => {
  console.log('ContentDetail mounted with config:', props.config)
  console.log('ContentDetail mounted with detail:', props.detail)
})

// 计算容器样式类
const containerClass = computed(() => {
  return props.config.containerClass || 'content-detail-container'
})

// 获取字段值
const getFieldValue = (field) => {
  // 添加参数验证
  if (!field) return null
  
  if (typeof field === 'string') {
    return props.detail[field]
  }
  
  if (field.key) {
    // 支持嵌套字段访问，如 'user.name'
    const keys = field.key.split('.')
    let value = props.detail
    for (const key of keys) {
      if (value && value[key] !== undefined) {
        value = value[key]
      } else {
        return null
      }
    }
    return value
  }
  
  return null
}

// 格式化字段值
const formatFieldValue = (field, value) => {
  if (!field || !value) return ''
  
  // 日期格式化
  if (field.type === 'date') {
    if (typeof value === 'string' && value.includes(' ')) {
      return value.split(' ')[0]
    }
    const date = new Date(value)
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString('zh-CN')
    }
  }
  
  // 日期时间格式化
  if (field.type === 'datetime') {
    const date = new Date(value)
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
  
  // 自定义格式化函数
  if (field.formatter && typeof field.formatter === 'function') {
    return field.formatter(value)
  }
  
  return value
}

// 检查是否有信息区域数据
const hasInfoData = () => {
  if (!props.config.infoSection || !props.config.infoSection.fields || !Array.isArray(props.config.infoSection.fields)) return false
  return props.config.infoSection.fields.some(field => field && getFieldValue(field))
}

// 检查是否有附件
const hasAttachments = () => {
  const attachments = getAttachments()
  return attachments && attachments.length > 0
}

// 获取附件列表
const getAttachments = () => {
  if (!props.config.attachmentField) return []
  
  const attachments = props.detail[props.config.attachmentField]
  if (!attachments) return []
  
  return Array.isArray(attachments) ? attachments : [attachments]
}

// 获取附件名称
const getAttachmentName = (item, index) => {
  if (typeof item === 'object' && item.name) {
    return item.name
  }
  return `附件${index + 1}`
}

// 获取文件URL
const getFileUrl = (item) => {
  if (typeof item === 'string') {
    if (item.startsWith('http')) {
      return item
    }
    return `/api/files/${item}`
  }
  
  if (typeof item === 'object') {
    if (item.url && item.url.startsWith('http')) {
      return item.url
    }
    if (item.url) {
      return `/api/files/${item.url}`
    }
    if (item.path) {
      return `/api/files/${item.path}`
    }
  }
  
  return ''
}

// 返回上一页
const goBack = () => {
  if (props.config.backHandler && typeof props.config.backHandler === 'function') {
    props.config.backHandler(router)
  } else {
    router.go(-1)
  }
}
</script>

<style scoped>
.content-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
}

.back-to-list-section {
  margin-bottom: 20px;
  padding: 0 20px;
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
  overflow-x: hidden;
}

.detail-wrapper {
  min-height: 500px;
  position: relative;
  overflow: hidden;
}

.detail-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  min-height: 400px;
}

.detail-title {
  font-size: 28px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.detail-meta {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #eee;
}

.meta-item {
  display: inline-block;
}

.meta-label {
  font-weight: 500;
}

.meta-value {
  color: #666;
}

.meta-divider {
  margin: 0 8px;
  color: #ddd;
}

.info-section {
  margin-bottom: 30px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px 20px 15px 0;
  position: relative;
}

.info-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--primary-color);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.info-table {
  width: 100%;
  padding-left: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.6;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  font-weight: 500;
  color: #333;
  text-align: right;
}

.info-value {
  flex: 1;
  color: #666;
  padding-left: 10px;
}

.detail-summary {
  background-color: #f8f8f8;
  padding: 15px 20px;
  margin-bottom: 30px;
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.summary-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.summary-content {
  color: #666;
  line-height: 1.6;
}

.detail-content-body {
  line-height: 1.8;
  color: #333;
  margin-bottom: 30px;
}

.detail-attachments {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px dashed #eee;
}

.attachments-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.attachment-item {
  margin-bottom: 8px;
}

.attachment-item a {
  color: #1890ff;
  text-decoration: none;
}

.attachment-item a:hover {
  text-decoration: underline;
}

.detail-actions {
  margin-top: 30px;
  text-align: center;
}

.empty-data {
  padding: 60px 0;
  text-align: center;
}

.empty-actions {
  margin-top: 20px;
}
</style>

<style>
.detail-content-body img {
  max-width: 100%;
  height: auto;
}

.detail-content-body p {
  margin-bottom: 16px;
}

.detail-content-body h1, 
.detail-content-body h2, 
.detail-content-body h3, 
.detail-content-body h4, 
.detail-content-body h5 {
  margin-top: 24px;
  margin-bottom: 16px;
}

.detail-content-body ul, 
.detail-content-body ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.detail-content-body table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

.detail-content-body table, 
.detail-content-body th, 
.detail-content-body td {
  border: 1px solid #ddd;
}

.detail-content-body th, 
.detail-content-body td {
  padding: 8px;
  text-align: left;
}
</style>
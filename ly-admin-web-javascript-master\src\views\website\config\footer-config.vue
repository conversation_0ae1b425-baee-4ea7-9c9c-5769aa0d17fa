<!--
  * 网站底部信息配置
-->
<template>
  <div class="footer-config-container">
    <a-spin :spinning="loading">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="basic" tab="基本信息">
          <a-form
            ref="basicFormRef"
            :model="formData"
            :rules="basicRules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="关于我们标题" name="aboutTitle">
              <a-input v-model:value="formData.aboutTitle" placeholder="请输入关于我们标题" />
            </a-form-item>

            <a-form-item label="关于我们内容" name="aboutContent">
              <a-textarea
                v-model:value="formData.aboutContent"
                placeholder="请输入关于我们内容"
                :rows="6"
              />
            </a-form-item>

            <a-form-item label="官方抖音二维码" name="douyinQrCode">
              <a-input v-model:value="formData.douyinQrCode" placeholder="请输入官方抖音二维码URL" />
              <div class="upload-wrapper">
                <SmartUpload
                  :limit="1"
                  :file-type="FILE_FOLDER_TYPE_ENUM.COMMON.value"
                  @upload-success="handleDouyinQrCodeUploadSuccess"
                />
                <div class="preview-image" v-if="formData.douyinQrCode">
                  <img :src="formData.douyinQrCode" alt="官方抖音二维码预览" />
                </div>
              </div>
            </a-form-item>

            <a-form-item label="官方公众号二维码" name="wechatQrCode">
              <a-input v-model:value="formData.wechatQrCode" placeholder="请输入官方公众号二维码URL" />
              <div class="upload-wrapper">
                <SmartUpload
                  :limit="1"
                  :file-type="FILE_FOLDER_TYPE_ENUM.COMMON.value"
                  @upload-success="handleWechatQrCodeUploadSuccess"
                />
                <div class="preview-image" v-if="formData.wechatQrCode">
                  <img :src="formData.wechatQrCode" alt="官方公众号二维码预览" />
                </div>
              </div>
            </a-form-item>

            <a-form-item :wrapper-col="{ span: 16, offset: 4 }">
              <a-button type="primary" @click="handleSubmit">保存</a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="links" tab="友情链接">
          <div class="operation-bar">
            <a-button type="primary" @click="handleAddLink">添加友情链接</a-button>
            <a-button type="primary" @click="handleSubmit" style="margin-left: 10px">保存</a-button>
          </div>

          <a-table
            :dataSource="friendlyLinks"
            :columns="linkColumns"
            :pagination="false"
            :rowKey="(record) => record.name + '_' + record.url"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'enabled'">
                <a-switch v-model:checked="record.enabled" />
              </template>

              <template v-if="column.dataIndex === 'action'">
                <a-button type="link" @click="handleEditLink(record, index)">编辑</a-button>
                <a-popconfirm
                  title="确定要删除这个友情链接吗？"
                  @confirm="handleDeleteLink(index)"
                  okText="确定"
                  cancelText="取消"
                >
                  <a-button type="link" danger>删除</a-button>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-spin>

    <!-- 友情链接编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :maskClosable="false"
    >
      <a-form
        ref="linkFormRef"
        :model="linkFormData"
        :rules="linkRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="名称" name="name">
          <a-input v-model:value="linkFormData.name" placeholder="请输入名称" />
        </a-form-item>

        <a-form-item label="链接URL" name="url">
          <a-input v-model:value="linkFormData.url" placeholder="请输入链接URL" />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="linkFormData.sort" :min="0" :max="999" style="width: 100%" />
        </a-form-item>

        <a-form-item label="是否启用" name="enabled">
          <a-switch v-model:checked="linkFormData.enabled" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { websiteConfigApi } from '/@/api/website/website-config-api';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import SmartUpload from '/@/components/framework/smart-upload/index.vue';

const basicFormRef = ref();
const linkFormRef = ref();
const loading = ref(false);
const activeTab = ref('basic');
const modalVisible = ref(false);
const modalTitle = ref('添加友情链接');
const editingIndex = ref(-1);

const formData = ref({
  aboutTitle: '',
  aboutContent: '',
  douyinQrCode: '',
  wechatQrCode: '',
  friendlyLinks: []
});

const friendlyLinks = ref([]);

const linkFormData = ref({
  name: '',
  url: '',
  sort: 0,
  enabled: true
});

const basicRules = {
  aboutTitle: [{ required: true, message: '请输入关于我们标题', trigger: 'blur' }]
};

const linkRules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  url: [{ required: true, message: '请输入链接URL', trigger: 'blur' }]
};

const linkColumns = [
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '链接URL',
    dataIndex: 'url',
    ellipsis: true
  },
  {
    title: '是否启用',
    dataIndex: 'enabled',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150
  }
];

// 获取底部信息配置
const fetchConfig = async () => {
  loading.value = true;
  try {
    const res = await websiteConfigApi.getWebsiteFooterConfig();
    if (res.data) {
      formData.value = { ...formData.value, ...res.data };
      friendlyLinks.value = res.data.friendlyLinks || [];
    }
  } catch (error) {
    console.error('获取底部信息配置失败:', error);
    message.error('获取底部信息配置失败');
  } finally {
    loading.value = false;
  }
};

// 处理抖音二维码上传成功
const handleDouyinQrCodeUploadSuccess = (fileList) => {
  console.log('抖音二维码上传成功，返回数据:', fileList);
  if (fileList && fileList.length > 0) {
    const file = fileList[0];
    // 优先使用 fileUrl，如果没有则尝试使用 url
    if (file.fileUrl) {
      formData.value.douyinQrCode = file.fileUrl;
      console.log('使用 fileUrl:', file.fileUrl);
    } else if (file.url) {
      formData.value.douyinQrCode = file.url;
      console.log('使用 url:', file.url);
    } else {
      console.error('抖音二维码上传成功但未获取到URL:', file);
      message.warning('图片上传成功，但未获取到URL');
      return;
    }

    // 手动触发表单验证
    if (basicFormRef.value) {
      setTimeout(() => {
        basicFormRef.value.validateFields(['douyinQrCode']);
      }, 100);
    }
  }
};

// 处理微信二维码上传成功
const handleWechatQrCodeUploadSuccess = (fileList) => {
  console.log('微信二维码上传成功，返回数据:', fileList);
  if (fileList && fileList.length > 0) {
    const file = fileList[0];
    // 优先使用 fileUrl，如果没有则尝试使用 url
    if (file.fileUrl) {
      formData.value.wechatQrCode = file.fileUrl;
      console.log('使用 fileUrl:', file.fileUrl);
    } else if (file.url) {
      formData.value.wechatQrCode = file.url;
      console.log('使用 url:', file.url);
    } else {
      console.error('微信二维码上传成功但未获取到URL:', file);
      message.warning('图片上传成功，但未获取到URL');
      return;
    }

    // 手动触发表单验证
    if (basicFormRef.value) {
      setTimeout(() => {
        basicFormRef.value.validateFields(['wechatQrCode']);
      }, 100);
    }
  }
};

// 添加友情链接
const handleAddLink = () => {
  modalTitle.value = '添加友情链接';
  editingIndex.value = -1;
  linkFormData.value = {
    name: '',
    url: '',
    sort: friendlyLinks.value.length,
    enabled: true
  };
  modalVisible.value = true;
};

// 编辑友情链接
const handleEditLink = (record, index) => {
  modalTitle.value = '编辑友情链接';
  editingIndex.value = index;
  linkFormData.value = { ...record };
  modalVisible.value = true;
};

// 删除友情链接
const handleDeleteLink = (index) => {
  friendlyLinks.value.splice(index, 1);
  message.success('删除成功');
};

// 弹窗确认
const handleModalOk = () => {
  linkFormRef.value.validate().then(() => {
    if (editingIndex.value === -1) {
      // 添加
      friendlyLinks.value.push({ ...linkFormData.value });
    } else {
      // 编辑
      friendlyLinks.value[editingIndex.value] = { ...linkFormData.value };
    }
    modalVisible.value = false;
    message.success(editingIndex.value === -1 ? '添加成功' : '编辑成功');
  });
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 提交表单
const handleSubmit = async () => {
  if (activeTab.value === 'basic') {
    basicFormRef.value.validate().then(async () => {
      await saveConfig();
    });
  } else {
    await saveConfig();
  }
};

// 保存配置
const saveConfig = async () => {
  loading.value = true;
  try {
    const config = {
      ...formData.value,
      friendlyLinks: friendlyLinks.value
    };
    const res = await websiteConfigApi.updateWebsiteFooterConfig(config);
    if (res.code === 0) {
      message.success('保存成功');
    } else {
      message.error(res.msg || '保存失败');
    }
  } catch (error) {
    console.error('保存底部信息配置失败:', error);
    message.error('保存失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchConfig();
});

defineExpose({
  fetchConfig
});
</script>

<style lang="less" scoped>
.footer-config-container {
  padding: 20px;
}

.operation-bar {
  margin-bottom: 20px;
}

.upload-wrapper {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.preview-image {
  margin-left: 20px;
  width: 120px;
  height: 120px;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>

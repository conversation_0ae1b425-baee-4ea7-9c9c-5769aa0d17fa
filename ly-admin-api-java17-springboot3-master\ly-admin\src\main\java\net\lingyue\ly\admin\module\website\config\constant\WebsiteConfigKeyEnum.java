package net.lingyue.ly.admin.module.website.config.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lingyue.ly.base.common.enumeration.BaseEnum;

/**
 * 网站配置常量类
 */
@Getter
@AllArgsConstructor
public enum WebsiteConfigKeyEnum implements BaseEnum {

    /**
     * 网站基本信息配置
     */
    WEBSITE_BASIC_CONFIG("website_basic_config", "网站基本信息配置"),
    
    /**
     * 网站轮播图配置
     */
    WEBSITE_CAROUSEL_CONFIG("website_carousel_config", "网站轮播图配置"),
    
    /**
     * 网站底部信息配置
     */
    WEBSITE_FOOTER_CONFIG("website_footer_config", "网站底部信息配置"),
    
    /**
     * 网站主题颜色配置
     */
    WEBSITE_THEME_CONFIG("website_theme_config", "网站主题颜色配置"),
    ;

    private final String value;

    private final String desc;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lingyue.ly.admin.module.website.branch.dao.BranchDao">

    <!-- 查询所有分会 -->
    <select id="queryAll" resultType="net.lingyue.ly.admin.module.website.branch.domain.entity.BranchEntity">
        SELECT * FROM t_website_branch
        WHERE deleted_flag = #{deletedFlag}
        ORDER BY sort ASC
    </select>

</mapper>

package net.lingyue.ly.admin.module.website.branch.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lingyue.ly.admin.module.website.branch.domain.entity.BranchEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分会DAO
 */
@Mapper
public interface BranchDao extends BaseMapper<BranchEntity> {

    /**
     * 查询所有分会
     *
     * @param deletedFlag 是否删除
     * @return 分会列表
     */
    List<BranchEntity> queryAll(@Param("deletedFlag") Boolean deletedFlag);
}

-- 内容类型表
CREATE TABLE IF NOT EXISTS `t_website_content_type` (
  `content_type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容类型ID',
  `content_type_name` varchar(50) NOT NULL COMMENT '类型名称',
  `content_type_code` varchar(50) NOT NULL COMMENT '类型编码',
  `content_type_desc` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `enable_flag` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`content_type_id`),
  UNIQUE KEY `uk_content_type_name` (`content_type_name`),
  UNIQUE KEY `uk_content_type_code` (`content_type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容类型表';

-- 内容表
CREATE TABLE IF NOT EXISTS `t_website_content` (
  `content_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `content_type_id` bigint(20) NOT NULL COMMENT '内容类型ID',
  `title` varchar(200) NOT NULL COMMENT '标题',
  `summary` varchar(500) DEFAULT NULL COMMENT '摘要',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `top_flag` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `recommend_flag` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `scheduled_publish_flag` tinyint(1) DEFAULT '0' COMMENT '是否定时发布',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `content_text` longtext COMMENT '内容 纯文本',
  `content_html` longtext COMMENT '内容 html',
  `attachment` varchar(1000) DEFAULT NULL COMMENT '附件',
  `page_view_count` int(11) DEFAULT '0' COMMENT '页面浏览量',
  `source` varchar(100) DEFAULT NULL COMMENT '来源',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `seo_keywords` varchar(200) DEFAULT NULL COMMENT 'SEO关键词',
  `seo_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态：0-草稿，1-已发布，2-已下线',
  `deleted_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`content_id`),
  KEY `idx_content_type_id` (`content_type_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容表';

-- 初始化内容类型数据
INSERT INTO `t_website_content_type` (`content_type_name`, `content_type_code`, `content_type_desc`, `sort`, `enable_flag`, `update_time`, `create_time`) VALUES
('新闻资讯', 'news', '网站新闻资讯', 1, 1, NOW(), NOW()),
('政策发布', 'policy', '政策法规发布', 2, 1, NOW(), NOW()),
('学会通知', 'notice', '学会通知公告', 3, 1, NOW(), NOW()),
('法规文件', 'regulation', '法规文件', 4, 1, NOW(), NOW()),
('学术活动', 'academic', '学术活动', 5, 1, NOW(), NOW()),
('党建动态', 'party_news', '党建工作动态信息', 6, 1, NOW(), NOW()),
('理论学习', 'party_theory', '党建理论学习资料', 7, 1, NOW(), NOW()),
('活动信息', 'activity', '学会活动信息', 8, 1, NOW(), NOW());

-- 添加菜单权限
INSERT INTO `t_menu` (`menu_id`, `parent_id`, `menu_name`, `path`, `component`, `api_perms`, `icon`, `sort`, `visible_flag`, `menu_type`, `create_time`, `update_time`, `deleted_flag`, `perms_type`, `disabled_flag`, `frame_flag`, `create_user_id`, `update_user_id`) VALUES
(1001, 1000, '内容管理', '/website/content', 'website/content/content-list', 'website:content:query', 'FileTextOutlined', 1, 1, 2, NOW(), NOW(), 0, 1, 0, 0, 1, 1);

INSERT INTO `t_menu` (`menu_id`, `parent_id`, `menu_name`, `path`, `component`, `api_perms`, `icon`, `sort`, `visible_flag`, `menu_type`, `create_time`, `update_time`, `deleted_flag`, `perms_type`, `disabled_flag`, `frame_flag`, `create_user_id`, `update_user_id`) VALUES
(1002, 1001, '内容添加', NULL, NULL, 'website:content:add', NULL, 1, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1003, 1001, '内容修改', NULL, NULL, 'website:content:update', NULL, 2, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1004, 1001, '内容删除', NULL, NULL, 'website:content:delete', NULL, 3, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1005, 1001, '内容类型管理', NULL, NULL, 'website:content:type:query', NULL, 4, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1006, 1001, '内容类型添加', NULL, NULL, 'website:content:type:add', NULL, 5, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1007, 1001, '内容类型修改', NULL, NULL, 'website:content:type:update', NULL, 6, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1),
(1008, 1001, '内容类型删除', NULL, NULL, 'website:content:type:delete', NULL, 7, 1, 3, NOW(), NOW(), 0, 1, 0, 0, 1, 1);

-- 添加角色菜单权限
INSERT INTO `t_role_menu` (`role_id`, `menu_id`, `create_time`)
SELECT 1, menu_id, NOW() FROM `t_menu` WHERE menu_id BETWEEN 1001 AND 1008;
-- 添加服务中心内容类型
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '服务中心',
             'service',
             '门户网站服务中心模块内容',
             9,
             1,
             NOW(),
             NOW()
         );

-- 添加政策相关内容类型
-- 注意：政策发布(policy)类型已经存在，不需要重复添加

-- 添加法律法规内容类型
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '法律法规',
             'law',
             '法律法规文件',
             10,
             1,
             NOW(),
             NOW()
         );

-- 添加规章制度内容类型
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '规章制度',
             'rules',
             '规章制度文件',
             11,
             1,
             NOW(),
             NOW()
         );

-- 添加学会相关内容类型
-- 学会简介
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '学会简介',
             'association_intro',
             '学会简介内容',
             12,
             1,
             NOW(),
             NOW()
         );

-- 组织机构
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '组织机构',
             'association_organization',
             '学会组织机构信息',
             13,
             1,
             NOW(),
             NOW()
         );

-- 章程制度
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '章程制度',
             'association_statute',
             '学会章程制度内容',
             14,
             1,
             NOW(),
             NOW()
         );

-- 联系我们
INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '联系我们',
             'association_contact',
             '学会联系方式信息',
             15,
             1,
             NOW(),
             NOW()
         );

INSERT INTO `t_website_content_type` (
    `content_type_name`,
    `content_type_code`,
    `content_type_desc`,
    `sort`,
    `enable_flag`,
    `update_time`,
    `create_time`
) VALUES (
             '联系我们',
             'management_system',
             '学会联系方式信息',
             16,
             1,
             NOW(),
             NOW()
         );

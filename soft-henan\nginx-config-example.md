# Nginx 配置示例

## 前端项目访问前缀配置

当前项目已配置了 `/soft-henan/` 访问前缀，可以通过以下Nginx配置实现代理：

### 基础配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 配置soft-henan前端项目
    location /soft-henan/ {
        # 代理到前端服务
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理WebSocket（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 如果有其他项目，可以继续添加
    # location /other-project/ {
    #     proxy_pass http://localhost:3001/;
    #     # ... 其他配置
    # }
}
```

### 生产环境配置（静态文件）

如果是生产环境部署静态文件：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # soft-henan项目静态文件
    location /soft-henan/ {
        alias /path/to/your/dist/;
        index index.html;
        try_files $uri $uri/ /soft-henan/index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理（包含文件上传支持）
    location /soft-api/ {
        proxy_pass http://localhost:1024/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传支持
        client_max_body_size 100m;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # 文件访问代理 - 直接代理到文件上传目录
    location /soft-api/upload/ {
        proxy_pass http://localhost:1024/upload/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存静态文件
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 部署步骤

1. **开发环境测试**：
   ```bash
   npm run dev
   # 访问 http://localhost:3000/soft-henan/
   ```

2. **生产环境构建**：
   ```bash
   npm run build
   # 构建文件会输出到 dist/ 目录
   ```

3. **配置Nginx并重启**：
   ```bash
   sudo nginx -t  # 测试配置
   sudo nginx -s reload  # 重新加载配置
   ```

## 注意事项

1. 确保所有的路由都会正确处理前缀
2. API请求也需要考虑前缀配置
3. 静态资源（图片、CSS、JS）路径会自动处理
4. 使用 `history` 路由模式时，需要配置 `try_files` 处理刷新问题 
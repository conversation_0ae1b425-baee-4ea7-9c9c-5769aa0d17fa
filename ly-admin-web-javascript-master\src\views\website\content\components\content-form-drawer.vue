<!--
  * 内容表单抽屉
-->
<template>
  <a-drawer
    :title="formData.contentId ? '编辑内容' : '新建内容'"
    :open="visibleFlag"
    :width="1000"
    :footerStyle="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="formData" :rules="formRules" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
      <a-form-item label="标题" name="title">
        <a-input v-model:value="formData.title" placeholder="请输入标题" />
      </a-form-item>
      <a-form-item label="分类" name="contentTypeId">
        <a-select v-model:value="formData.contentTypeId" style="width: 100%" :showSearch="true" :allowClear="true">
          <a-select-option v-for="item in contentTypeList" :key="item.contentTypeId" :value="item.contentTypeId">
            {{ item.contentTypeName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="分会" name="branchId">
        <a-select v-model:value="formData.branchId" style="width: 100%" :showSearch="true" :allowClear="true" placeholder="请选择分会">
          <a-select-option v-for="item in branchList" :key="item.branchId" :value="item.branchId">
            {{ item.branchName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="内容分类" name="categoryIds">
        <a-tree-select
          v-model:value="formData.categoryIds"
          style="width: 100%"
          :tree-data="categoryTreeData"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择内容分类"
          multiple
          tree-default-expand-all
          :tree-checkable="true"
          :show-checked-strategy="SHOW_CHILD"
        />
      </a-form-item>
      <a-form-item label="摘要">
        <a-textarea v-model:value="formData.summary" placeholder="请输入摘要" :rows="3" />
      </a-form-item>
      <a-form-item label="封面图片">
        <Upload
          :maxUploadSize="1"
          :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
          buttonText="上传封面图片"
          listType="picture-card"
          :maxSize="2"
          :accept="'.jpg,.jpeg,.png'"
          @change="changeCoverImage"
          :defaultFileList="coverImageList"
        />
      </a-form-item>
      <a-form-item label="作者" name="author">
        <a-input v-model:value="formData.author" placeholder="请输入作者" />
      </a-form-item>
      <a-form-item label="来源" name="source">
        <a-input v-model:value="formData.source" placeholder="请输入来源" />
      </a-form-item>
      <a-form-item label="是否置顶">
        <a-switch v-model:checked="formData.topFlag" checked-children="是" un-checked-children="否" />
      </a-form-item>
      <a-form-item label="是否推荐">
        <a-switch v-model:checked="formData.recommendFlag" checked-children="是" un-checked-children="否" />
      </a-form-item>
      <a-form-item label="定时发布">
        <a-switch
          v-model:checked="formData.scheduledPublishFlag"
          checked-children="开"
          un-checked-children="关"
          @change="changesSheduledPublishFlag"
        />
      </a-form-item>
      <a-form-item v-show="formData.scheduledPublishFlag" label="发布时间">
        <a-date-picker
          v-model:value="releaseTime"
          :format="timeFormat"
          showTime
          :allowClear="false"
          placeholder="请选择发布时间"
          style="width: 200px"
          @change="changeTime"
        />
      </a-form-item>

      <!-- 活动特有字段，只在内容类型为"活动信息"时显示 -->
      <template v-if="isActivityType">
        <a-divider orientation="left">活动信息</a-divider>

        <a-form-item label="活动开始时间" name="activityStartTime">
          <a-date-picker
            v-model:value="activityStartTime"
            :format="timeFormat"
            showTime
            placeholder="请选择活动开始时间"
            style="width: 200px"
            @change="changeActivityStartTime"
          />
        </a-form-item>

        <a-form-item label="活动结束时间" name="activityEndTime">
          <a-date-picker
            v-model:value="activityEndTime"
            :format="timeFormat"
            showTime
            placeholder="请选择活动结束时间"
            style="width: 200px"
            @change="changeActivityEndTime"
          />
        </a-form-item>

        <a-form-item label="活动地点" name="activityLocation">
          <a-input v-model:value="formData.activityLocation" placeholder="请输入活动地点" />
        </a-form-item>

        <a-form-item label="主办单位" name="activityOrganizer">
          <a-input v-model:value="formData.activityOrganizer" placeholder="请输入主办单位" />
        </a-form-item>

        <a-form-item label="联系方式" name="activityContact">
          <a-input v-model:value="formData.activityContact" placeholder="请输入联系方式" />
        </a-form-item>
      </template>
      <a-form-item label="状态" name="status">
        <a-select v-model:value="formData.status" placeholder="请选择状态">
          <a-select-option v-for="(value, key) in CONTENT_STATUS_ENUM" :key="key" :value="value.value">
            {{ value.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="SEO关键词">
        <a-input v-model:value="formData.seoKeywords" placeholder="请输入SEO关键词，多个关键词用英文逗号分隔" />
      </a-form-item>
      <a-form-item label="SEO描述">
        <a-textarea v-model:value="formData.seoDescription" placeholder="请输入SEO描述" :rows="2" />
      </a-form-item>
      <a-form-item label="内容" name="contentHtml">
        <SmartWangeditor ref="contentRef" :modelValue="formData.contentHtml" :height="300" />
      </a-form-item>
      <a-form-item label="附件">
        <Upload
          :defaultFileList="defaultFileList"
          :maxUploadSize="10"
          :folder="FILE_FOLDER_TYPE_ENUM.NOTICE.value"
          buttonText="上传附件"
          listType="text"
          extraMsg="最多上传10个附件"
          @change="changeAttachment"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
  import { reactive, ref, onMounted, watch, computed, nextTick } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import _ from 'lodash';
  import dayjs from 'dayjs';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
  import { contentApi } from '/@/api/website/content-api';
  import { fileApi } from '/@/api/support/file-api';
  import { CONTENT_STATUS_ENUM } from '/@/constants/website/content-const';
  import SmartWangeditor from '/@/components/framework/wangeditor/index.vue';
  import Upload from '/@/components/support/file-upload/index.vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { TreeSelect } from 'ant-design-vue';
  import { getAllBranch } from '/@/api/website/branch';
  import { getContentCategoryTree } from '/@/api/website/content';

  const emits = defineEmits(['reloadList']);

  // TreeSelect 常量
  const SHOW_CHILD = TreeSelect.SHOW_CHILD;

  // ------------------ 显示，关闭 ------------------
  // 显示
  const visibleFlag = ref(false);
  function showModal(contentId) {
    Object.assign(formData, defaultFormData);
    releaseTime.value = null;
    activityStartTime.value = null;
    activityEndTime.value = null;
    defaultFileList.value = [];
    coverImageList.value = [];
    queryContentTypeList();
    queryBranchList();
    queryCategoryTree();
    if (contentId) {
      getContentUpdate(contentId);
    }

    visibleFlag.value = true;
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    });
  }

  // 关闭
  function onClose() {
    visibleFlag.value = false;
  }

  // ------------------ 表单 ------------------

  const formRef = ref();
  const contentRef = ref();

  const defaultFormData = {
    contentId: undefined,
    contentTypeId: undefined,
    title: undefined, // 标题
    summary: undefined, // 摘要
    coverImage: undefined, // 封面图片
    topFlag: false, // 是否置顶
    recommendFlag: false, // 是否推荐
    scheduledPublishFlag: false, // 是否定时发布
    publishTime: undefined, // 发布时间
    attachment: [], // 附件
    contentHtml: '', // html内容
    contentText: '', // 纯文本内容
    source: undefined, // 来源
    author: undefined, // 作者
    seoKeywords: undefined, // SEO关键词
    seoDescription: undefined, // SEO描述
    status: CONTENT_STATUS_ENUM.DRAFT.value, // 状态
    // 分会和分类
    branchId: undefined, // 分会ID
    categoryIds: [], // 分类ID列表
    // 活动特有字段
    activityStartTime: undefined, // 活动开始时间
    activityEndTime: undefined, // 活动结束时间
    activityLocation: undefined, // 活动地点
    activityOrganizer: undefined, // 主办单位
    activityContact: undefined, // 联系方式
  };

  const formData = reactive({ ...defaultFormData });

  const formRules = {
    title: [{ required: true, message: '请输入标题' }],
    contentTypeId: [{ required: true, message: '请选择分类' }],
    source: [{ required: true, message: '请输入来源' }],
    author: [{ required: true, message: '请输入作者' }],
    contentHtml: [{ required: true, message: '请输入内容' }],
    status: [{ required: true, message: '请选择状态' }],
  };

  // 查询详情
  async function getContentUpdate(contentId) {
    try {
      SmartLoading.show();
      const result = await contentApi.getUpdateContentInfo(contentId);
      console.log('获取内容详情:', result.data);

      // 处理附件
      const attachment = result.data.attachment;
      if (!_.isEmpty(attachment)) {
        defaultFileList.value = attachment;
      } else {
        defaultFileList.value = [];
      }

      // 处理封面图片
      if (result.data.coverImage) {
        try {
          const coverImage = result.data.coverImage;
          console.log('获取到的封面图片:', coverImage);

          // 判断coverImage是URL还是ID
          if (typeof coverImage === 'string' && (coverImage.startsWith('http') || coverImage.startsWith('/'))) {
            // 如果是URL，直接创建文件对象
            coverImageList.value = [{
              fileName: '封面图片',
              fileType: coverImage.toLowerCase().endsWith('.png') ? 'png' : 'jpg',
              fileUrl: coverImage,
              url: coverImage, // 同时设置url属性，确保组件能正确显示
              folderType: FILE_FOLDER_TYPE_ENUM.COMMON.value
            }];
            console.log('使用URL创建封面图片对象:', coverImageList.value);
          } else {
            // 假设是文件ID，尝试获取文件详情
            try {
              const fileId = typeof coverImage === 'string' ? coverImage : coverImage.toString();
              const fileResult = await fileApi.getFileDetail(fileId);

              if (fileResult && fileResult.data) {
                // 设置文件列表
                const fileData = fileResult.data;
                // 确保fileUrl和url都存在
                if (!fileData.url && fileData.fileUrl) {
                  fileData.url = fileData.fileUrl;
                } else if (!fileData.fileUrl && fileData.url) {
                  fileData.fileUrl = fileData.url;
                }
                coverImageList.value = [fileData];
                console.log('从文件服务获取的封面图片详情:', fileData);
              } else {
                // 如果获取详情失败，创建一个带下载URL的文件对象
                const downloadUrl = `/api/support/file/download/${fileId}`;
                coverImageList.value = [{
                  fileId: fileId,
                  fileName: '封面图片',
                  fileType: 'jpg',
                  fileUrl: downloadUrl,
                  url: downloadUrl, // 同时设置url属性
                  folderType: FILE_FOLDER_TYPE_ENUM.COMMON.value
                }];
                console.log('创建带下载URL的封面图片对象:', coverImageList.value);
              }
            } catch (detailErr) {
              console.warn('获取文件详情失败，使用简单对象:', detailErr);
              // 创建一个简单的文件对象，使用构造的下载URL
              const fileId = typeof coverImage === 'string' ? coverImage : coverImage.toString();
              const downloadUrl = `/api/support/file/download/${fileId}`;
              coverImageList.value = [{
                fileId: fileId,
                fileName: '封面图片',
                fileType: 'jpg',
                fileUrl: downloadUrl,
                url: downloadUrl, // 同时设置url属性
                folderType: FILE_FOLDER_TYPE_ENUM.COMMON.value
              }];
            }
          }
        } catch (fileErr) {
          console.error('处理封面图片失败:', fileErr);
          coverImageList.value = [];
        }
      } else {
        coverImageList.value = [];
      }

      // 将数据赋值给表单
      Object.assign(formData, result.data);

      // 设置发布时间
      releaseTime.value = dayjs(result.data.publishTime);

      // 设置活动时间
      if (result.data.activityStartTime) {
        activityStartTime.value = dayjs(result.data.activityStartTime);
      }
      if (result.data.activityEndTime) {
        activityEndTime.value = dayjs(result.data.activityEndTime);
      }

      visibleFlag.value = true;
    } catch (err) {
      console.error('获取内容详情失败:', err);
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      if (contentRef.value) {
        formData.contentHtml = contentRef.value.getHtml();
        formData.contentText = contentRef.value.getText();
      }
      if (formRef.value) {
        await formRef.value.validateFields();
        save();
      } else {
        message.error('表单未初始化完成，请稍后再试');
      }
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    try {
      SmartLoading.show();

      // 确保发布时间存在
      if (!formData.publishTime) {
        formData.publishTime = dayjs().format(timeFormat);
      }

      // 创建一个新对象，只包含需要提交的数据
      const submitData = _.cloneDeep(formData);

      // 打印提交的数据，便于调试
      console.log('提交的表单数据:', submitData);

      let res;
      if (formData.contentId) {
        res = await contentApi.updateContent(submitData);
      } else {
        res = await contentApi.addContent(submitData);
      }

      if (res && (res.code === 0 || res.code === 1)) {
        message.success('保存成功');
        emits('reloadList');
        onClose();
      } else {
        message.error(res?.msg || '保存失败');
      }
    } catch (err) {
      console.error('保存内容失败:', err);
      message.error('保存失败，请检查表单数据');
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // ------------------ 内容分类 ------------------

  // 查询分类列表
  const contentTypeList = ref([]);
  async function queryContentTypeList() {
    try {
      const result = await contentApi.getAllContentTypes();
      contentTypeList.value = result.data;
      if (contentTypeList.value.length > 0 && !formData.contentId) {
        formData.contentTypeId = contentTypeList.value[0].contentTypeId;
      }
    } catch (err) {
      smartSentry.captureError(err);
    }
  }

  // ------------------ 分会 ------------------

  // 查询分会列表
  const branchList = ref([]);
  async function queryBranchList() {
    try {
      const result = await getAllBranch();
      if (result.code === 0 && result.data) {
        branchList.value = result.data;
      } else {
        branchList.value = [];
      }
    } catch (err) {
      console.error('获取分会列表失败:', err);
      smartSentry.captureError(err);
      branchList.value = [];
    }
  }

  // ------------------ 内容分类树 ------------------

  // 查询内容分类树
  const categoryTreeData = ref([]);
  async function queryCategoryTree() {
    try {
      const result = await getContentCategoryTree({});
      if (result.code === 0 && result.data) {
        categoryTreeData.value = result.data;
      } else {
        categoryTreeData.value = [];
      }
    } catch (err) {
      console.error('获取内容分类树失败:', err);
      smartSentry.captureError(err);
      categoryTreeData.value = [];
    }
  }

  // ----------------------- 判断是否为活动类型 ----------------------------
  // 判断当前选择的内容类型是否为"活动信息"
  const isActivityType = computed(() => {
    if (!contentTypeList.value || contentTypeList.value.length === 0 || !formData.contentTypeId) {
      return false;
    }

    const selectedType = contentTypeList.value.find(type => type.contentTypeId === formData.contentTypeId);
    return selectedType && (selectedType.contentTypeCode === 'activity' || selectedType.contentTypeName === '活动信息');
  });

  // 监听内容类型变化
  watch(() => formData.contentTypeId, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      console.log('内容类型变化:', newVal, '是否为活动类型:', isActivityType.value);
    }
  });

  // ----------------------- 发布时间 ----------------------------
  const timeFormat = 'YYYY-MM-DD HH:mm:ss';
  const releaseTime = ref(null);
  function changeTime(_, dateString) {
    // 使用下划线(_)表示不使用的参数
    formData.publishTime = dateString;
  }
  function changesSheduledPublishFlag(checked) {
    releaseTime.value = checked ? dayjs() : null;
    formData.publishTime = checked ? dayjs().format(timeFormat) : null;
  }

  // ----------------------- 活动时间 ----------------------------
  const activityStartTime = ref(null);
  const activityEndTime = ref(null);

  function changeActivityStartTime(_, dateString) {
    formData.activityStartTime = dateString;
  }

  function changeActivityEndTime(_, dateString) {
    formData.activityEndTime = dateString;
  }

  // ----------------------- 上传附件 ----------------------------
  // 已上传的附件列表
  const defaultFileList = ref([]);
  function changeAttachment(fileList) {
    defaultFileList.value = fileList;

    // 处理附件数据，确保只传递必要的字段
    if (_.isEmpty(fileList)) {
      formData.attachment = [];
    } else {
      // 只保留后端需要的字段，避免传递过多数据
      formData.attachment = fileList.map(file => ({
        fileId: file.fileId,
        fileName: file.fileName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        fileKey: file.fileKey,
        folderType: file.folderType
      }));

      // 打印处理后的附件数据，便于调试
      console.log('处理后的附件数据:', formData.attachment);
    }
  }

  // ----------------------- 上传封面图片 ----------------------------
  // 已上传的封面图片
  const coverImageList = ref([]);
  function changeCoverImage(fileList) {
    coverImageList.value = fileList;

    // 处理封面图片数据，保存图片URL而不是ID
    if (_.isEmpty(fileList)) {
      formData.coverImage = null;
    } else {
      const imageFile = fileList[0];

      // 保存图片URL而不是ID
      if (imageFile.fileUrl) {
        // 使用完整的URL
        formData.coverImage = imageFile.fileUrl;
      } else if (imageFile.url) {
        // 有些情况下可能是url字段
        formData.coverImage = imageFile.url;
      } else if (imageFile.fileId) {
        // 如果没有URL但有ID，可以构造一个URL
        formData.coverImage = `/api/support/file/download/${imageFile.fileId}`;
      } else {
        formData.coverImage = null;
      }

      // 打印处理后的封面图片数据，便于调试
      console.log('处理后的封面图片数据:', formData.coverImage);
    }
  }

  // ----------------------- 以下是暴露的方法内容 ------------------------
  defineExpose({
    showModal,
  });
</script>

<style lang="less" scoped></style>

package net.lingyue.ly.admin.module.website.branch.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.module.website.branch.dao.BranchDao;
import net.lingyue.ly.admin.module.website.branch.domain.entity.BranchEntity;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchAddForm;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchQueryForm;
import net.lingyue.ly.admin.module.website.branch.domain.form.BranchUpdateForm;
import net.lingyue.ly.admin.module.website.branch.domain.vo.BranchVO;
import net.lingyue.ly.admin.module.website.content.dao.ContentDao;
import net.lingyue.ly.base.common.domain.PageParam;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartBeanUtil;
import net.lingyue.ly.base.common.util.SmartPageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分会服务
 */
@Slf4j
@Service
public class BranchService {

    @Resource
    private BranchDao branchDao;

    @Resource
    private ContentDao contentDao;

    /**
     * 添加分会
     *
     * @param addForm 添加表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(BranchAddForm addForm) {
        // 检查分会名称是否重复
        LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BranchEntity::getBranchName, addForm.getBranchName())
                .eq(BranchEntity::getDeletedFlag, false);
        if (branchDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("分会名称已存在");
        }

        // 检查分会编码是否重复
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BranchEntity::getBranchCode, addForm.getBranchCode())
                .eq(BranchEntity::getDeletedFlag, false);
        if (branchDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("分会编码已存在");
        }

        // 创建分会实体
        BranchEntity branchEntity = SmartBeanUtil.copy(addForm, BranchEntity.class);
        branchEntity.setDisabledFlag(false);
        branchEntity.setDeletedFlag(false);
        branchEntity.setSort(addForm.getSort() == null ? 0 : addForm.getSort());

        // 显式设置 Logo 字段
        branchEntity.setLogo(addForm.getLogo());

        // 打印日志，用于调试
        log.info("添加分会，Logo URL: {}", addForm.getLogo());

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        branchEntity.setCreateTime(now);
        branchEntity.setUpdateTime(now);

        branchDao.insert(branchEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新分会
     *
     * @param updateForm 更新表单
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(BranchUpdateForm updateForm) {
        BranchEntity branchEntity = branchDao.selectById(updateForm.getBranchId());
        if (branchEntity == null) {
            return ResponseDTO.userErrorParam("分会不存在");
        }

        // 检查分会名称是否重复
        if (!branchEntity.getBranchName().equals(updateForm.getBranchName())) {
            LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BranchEntity::getBranchName, updateForm.getBranchName())
                    .eq(BranchEntity::getDeletedFlag, false)
                    .ne(BranchEntity::getBranchId, updateForm.getBranchId());
            if (branchDao.selectCount(queryWrapper) > 0) {
                return ResponseDTO.userErrorParam("分会名称已存在");
            }
        }

        // 检查分会编码是否重复
        if (!branchEntity.getBranchCode().equals(updateForm.getBranchCode())) {
            LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BranchEntity::getBranchCode, updateForm.getBranchCode())
                    .eq(BranchEntity::getDeletedFlag, false)
                    .ne(BranchEntity::getBranchId, updateForm.getBranchId());
            if (branchDao.selectCount(queryWrapper) > 0) {
                return ResponseDTO.userErrorParam("分会编码已存在");
            }
        }

        // 更新分会实体
        branchEntity.setBranchName(updateForm.getBranchName());
        branchEntity.setBranchCode(updateForm.getBranchCode());
        branchEntity.setDescription(updateForm.getDescription());
        branchEntity.setLogo(updateForm.getLogo());
        branchEntity.setSort(updateForm.getSort() == null ? 0 : updateForm.getSort());
        if (updateForm.getDisabledFlag() != null) {
            branchEntity.setDisabledFlag(updateForm.getDisabledFlag());
        }
        branchEntity.setUpdateTime(LocalDateTime.now());

        branchDao.updateById(branchEntity);
        return ResponseDTO.ok();
    }

    /**
     * 删除分会
     *
     * @param branchId 分会ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long branchId) {
        BranchEntity branchEntity = branchDao.selectById(branchId);
        if (branchEntity == null) {
            return ResponseDTO.userErrorParam("分会不存在");
        }

        // 检查是否有内容关联该分会
        LambdaQueryWrapper<net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity::getBranchId, branchId)
                .eq(net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity::getDeletedFlag, false);
        if (contentDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("该分会下有内容，无法删除");
        }

        // 逻辑删除
        branchEntity.setDeletedFlag(true);
        branchEntity.setUpdateTime(LocalDateTime.now());
        branchDao.updateById(branchEntity);

        return ResponseDTO.ok();
    }

    /**
     * 获取分会详情
     *
     * @param branchId 分会ID
     * @return 分会详情
     */
    public ResponseDTO<BranchVO> getDetail(Long branchId) {
        BranchEntity branchEntity = branchDao.selectById(branchId);
        if (branchEntity == null || branchEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("分会不存在");
        }

        BranchVO branchVO = SmartBeanUtil.copy(branchEntity, BranchVO.class);
        return ResponseDTO.ok(branchVO);
    }

    /**
     * 分页查询分会
     *
     * @param pageParam 分页参数
     * @return 分页结果
     */
    public PageResult<BranchVO> queryPage(PageParam pageParam) {
        Page<?> page = SmartPageUtil.convert2PageQuery(pageParam);

        LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BranchEntity::getDeletedFlag, false)
                .orderByAsc(BranchEntity::getSort);

        Page<BranchEntity> branchPage = branchDao.selectPage((Page<BranchEntity>) page, queryWrapper);
        List<BranchVO> branchVOList = SmartBeanUtil.copyList(branchPage.getRecords(), BranchVO.class);

        return SmartPageUtil.convert2PageResult(branchPage, branchVOList);
    }

    /**
     * 分页查询分会（使用 BranchQueryForm）
     *
     * @param queryForm 查询表单
     * @return 分页结果
     */
    public PageResult<BranchVO> queryPage(BranchQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);

        LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        queryWrapper.eq(BranchEntity::getDeletedFlag, queryForm.getDeletedFlag() != null ? queryForm.getDeletedFlag() : false);

        // 如果有分会名称，添加模糊查询条件
        if (StringUtils.isNotBlank(queryForm.getBranchName())) {
            queryWrapper.like(BranchEntity::getBranchName, queryForm.getBranchName());
        }

        // 如果有分会编码，添加模糊查询条件
        if (StringUtils.isNotBlank(queryForm.getBranchCode())) {
            queryWrapper.like(BranchEntity::getBranchCode, queryForm.getBranchCode());
        }

        // 如果有禁用标志，添加查询条件
        if (queryForm.getDisabledFlag() != null) {
            queryWrapper.eq(BranchEntity::getDisabledFlag, queryForm.getDisabledFlag());
        }

        // 按排序字段升序排序
        queryWrapper.orderByAsc(BranchEntity::getSort);

        @SuppressWarnings("unchecked")
        Page<BranchEntity> branchPage = branchDao.selectPage((Page<BranchEntity>) page, queryWrapper);
        List<BranchVO> branchVOList = SmartBeanUtil.copyList(branchPage.getRecords(), BranchVO.class);

        return SmartPageUtil.convert2PageResult(branchPage, branchVOList);
    }

    /**
     * 获取所有分会
     *
     * @return 分会列表
     */
    public List<BranchVO> getAll() {
        LambdaQueryWrapper<BranchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BranchEntity::getDeletedFlag, false)
                .eq(BranchEntity::getDisabledFlag, false)
                .orderByAsc(BranchEntity::getSort);

        List<BranchEntity> branchList = branchDao.selectList(queryWrapper);
        return SmartBeanUtil.copyList(branchList, BranchVO.class);
    }
}

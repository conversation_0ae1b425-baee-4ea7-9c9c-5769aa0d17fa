import api from './index';
import { getAllContentTypes, queryContent, getContentByType, getContentDetail } from './content';
import axios from 'axios';

// 从摘要中提取信息
function extractInfoFromSummary(summary) {
  // 使用 "||" 作为字段分隔符
  const fields = summary.split('||');
  const result = {
    director: '张明',
    establishTime: '',
    contactPhone: '0371-XXXXXXXX',
    address: '河南省郑州市金水区文化路97号河南省人民医院',
    email: '<EMAIL>'
  };

  // 遍历每个字段，提取信息
  fields.forEach(field => {
    const trimmedField = field.trim();

    // 提取负责人
    if (trimmedField.startsWith('负责人:') || trimmedField.startsWith('负责人：') ||
        trimmedField.startsWith('主任委员:') || trimmedField.startsWith('主任委员：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.director = value;
    }

    // 提取成立时间
    else if (trimmedField.startsWith('成立时间:') || trimmedField.startsWith('成立时间：') ||
             trimmedField.includes('成立于')) {
      let value = '';
      if (trimmedField.includes('成立于')) {
        const match = trimmedField.match(/成立于\s*([0-9]{4}[年\s\./-][0-9]{1,2}[月]?)/);
        value = match ? match[1] : '';
      } else {
        value = trimmedField.split(/[：:]/)[1]?.trim();
      }
      if (value) result.establishTime = value;
    }

    // 提取联系电话
    else if (trimmedField.startsWith('电话:') || trimmedField.startsWith('电话：') ||
             trimmedField.startsWith('联系电话:') || trimmedField.startsWith('联系电话：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.contactPhone = value;
    }

    // 提取办公地址
    else if (trimmedField.startsWith('地址:') || trimmedField.startsWith('地址：') ||
             trimmedField.startsWith('办公地址:') || trimmedField.startsWith('办公地址：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.address = value;
    }

    // 提取电子邮箱
    else if (trimmedField.startsWith('邮箱:') || trimmedField.startsWith('邮箱：') ||
             trimmedField.startsWith('电子邮箱:') || trimmedField.startsWith('电子邮箱：')) {
      const value = trimmedField.split(/[：:]/)[1]?.trim();
      if (value) result.email = value;
    }
  });

  return result;
}

/**
 * 获取分会列表
 * @param {Object} params 查询参数
 * @returns {Promise<any>}
 */
export function getBranchList(params = {}) {
  // 先获取所有内容类型
  return getAllContentTypes()
    .then(res => {
      console.log('获取内容类型结果:', res);

      if (res && res.data) {
        // 查找分会类型
        const branchType = res.data.find(type =>
          type.contentTypeCode === 'branch' ||
          type.contentTypeName === '专业委员会' ||
          type.contentTypeName === '分会'
        );

        console.log('找到的分会类型:', branchType);

        if (branchType) {
          // 使用内容类型ID查询内容，设置默认值
          const queryParams = {
            // 强制设置默认值
            pageNum: 1,
            pageSize: 50,
            status: 1,
            deletedFlag: false,
            // 其他参数
            ...params,
            // 设置内容类型ID
            contentTypeId: branchType.contentTypeId,
            // 再次确保 pageNum 和 pageSize 是数字类型且不为 null
            pageNum: Number(params.pageNum || 1),
            pageSize: Number(params.pageSize || 50)
          };

          console.log('查询分会内容参数:', queryParams);

          // 查询内容列表
          return queryContent(queryParams)
            .then(contentRes => {
              console.log('获取分会列表结果:', contentRes);

              if (contentRes && contentRes.data && contentRes.data.list) {
                // 转换数据格式
                const branchList = contentRes.data.list.map(item => ({
                  id: item.contentId,
                  name: item.title,
                  isNew: item.topFlag || false, // 使用置顶标志作为"新"标志
                  description: item.summary || '',
                  createTime: item.createTime
                }));

                return {
                  code: contentRes.code,
                  data: {
                    list: branchList,
                    total: contentRes.data.total || branchList.length
                  },
                  msg: contentRes.msg
                };
              }

              // 如果没有数据，返回默认数据
              return getDefaultBranchList();
            })
            .catch(error => {
              console.error('查询分会内容失败:', error);
              return getDefaultBranchList();
            });
        }
      }

      // 如果没有找到分会类型，返回默认数据
      console.log('未找到分会类型，返回默认数据');
      return getDefaultBranchList();
    })
    .catch(error => {
      console.error('获取内容类型失败:', error);
      return getDefaultBranchList();
    });
}

// 获取默认分会列表数据
function getDefaultBranchList() {
  return {
    code: 1,
    data: [
      { id: 1, name: '标准化工作分会', isNew: true },
      { id: 2, name: '针刀医学分会', isNew: false },
      { id: 3, name: '逆袭糖尿病分会', isNew: false },
      { id: 4, name: '传承创新发展工作分会', isNew: false },
      { id: 5, name: '中医理疗分会', isNew: false },
      { id: 6, name: '基层服务能力与医师诊疗能力双提升分会', isNew: true },
      { id: 7, name: '天灸肺病防治学分会', isNew: false }
    ],
    msg: '操作成功(默认数据)'
  };
}

/**
 * 获取分会详情
 * @param {number} id 分会ID
 * @returns {Promise<any>}
 */
export function getBranchDetail(id) {
  // 使用内容管理API获取详情
  return getContentDetail(id)
    .then(res => {
      console.log('获取分会详情结果:', res);

      if (res && (res.code === 0 || res.code === 1) && res.data) {
        // 从摘要中提取信息
        const extractedInfo = extractInfoFromSummary(res.data.summary || '');

        // 转换数据格式
        return {
          code: res.code,
          data: {
            id: res.data.contentId,
            name: res.data.title,
            isNew: res.data.topFlag || false,
            description: res.data.summary || '',
            content: res.data.contentHtml,
            createTime: res.data.createTime,
            updateTime: res.data.updateTime,
            director: extractedInfo.director,
            establishTime: extractedInfo.establishTime || res.data.createTime?.substring(0, 7) || '',
            contactPhone: extractedInfo.contactPhone,
            address: extractedInfo.address,
            email: extractedInfo.email,
            viewCount: res.data.pageViewCount || Math.floor(Math.random() * 300) + 100,
            publishDate: res.data.createTime || '2024-05-20',
            source: '原创',
            author: '管理员'
          },
          msg: res.msg
        };
      }

      // 如果没有数据，返回默认数据
      return getDefaultBranchDetail(id);
    })
    .catch(error => {
      console.error('获取分会详情失败:', error);
      return getDefaultBranchDetail(id);
    });
}

// 获取默认分会详情数据
function getDefaultBranchDetail(id) {
  return {
    code: 1,
    data: {
      id: id,
      name: id === 1 ? '标准化工作委员会' :
            id === 2 ? '针刀医学分会' :
            id === 3 ? '逆袭糖尿病专业委员会' : '专业委员会',
      isNew: id === 1 || id === 12 || id === 6,
      description: '专注于相关领域的学术研究、技术推广与交流，致力于提高诊疗水平，推动学科发展。',
      content: `<p>河南省软组织病研究会${id === 1 ? '标准化工作委员会' : id === 2 ? '针刀医学分会' : id === 3 ? '逆袭糖尿病专业委员会' : '专业委员会'}是河南省软组织病研究会下设的专业委员会之一。委员会由我省相关领域的知名专家、学者组成，致力于开展学术研究、培训、交流及推广工作，提高诊疗水平，推动学科发展。</p>
      <h3>主要职责</h3>
      <ul>
        <li>组织开展学术交流、研讨及培训活动</li>
        <li>推广诊疗新技术、新方法</li>
        <li>编写临床诊疗指南与专家共识</li>
        <li>开展相关科研项目</li>
        <li>促进专业人才培养</li>
      </ul>
      <h3>委员会成员</h3>
      <p><strong>主任委员：</strong>张明 (主任医师，河南省人民医院)</p>
      <p><strong>副主任委员：</strong>李华 (主任医师，郑州大学第一附属医院)</p>
      <p><strong>副主任委员：</strong>王强 (主任医师，河南省中医院)</p>
      <p><strong>秘书长：</strong>刘伟 (副主任医师，郑州市第一人民医院)</p>`,
      createTime: '2023-05-28 02:50:01',
      updateTime: '2024-05-20 10:30:15',
      director: id === 1 ? '张明' : id === 2 ? '李华' : '王强',
      establishTime: id === 1 ? '2023年05月' : id === 2 ? '2022年10月' : '2021年08月',
      contactPhone: '0371-XXXXXXXX',
      address: '河南省郑州市金水区文化路97号河南省人民医院',
      email: '<EMAIL>',
      viewCount: Math.floor(Math.random() * 300) + 100,
      publishDate: '2024-05-20',
      source: '原创',
      author: '管理员'
    },
    msg: '操作成功(默认数据)'
  };
}

/**
 * 从后台管理系统获取分会列表
 * @returns {Promise<any>}
 */
export function getBackendBranchList() {
  return api.get('/website/branch/getAll')
    .then(res => {
      console.log('从后台获取分会列表结果:', res);

      if (res && res.code === 0 && res.data) {
        // 转换数据格式
        const branchList = res.data.map(item => ({
          id: item.branchId,
          name: item.branchName,
          isNew: false, // 后台没有新标志，默认为false
          description: item.description || '',
          code: item.branchCode || '',
          logo: item.logo || '',
          sort: item.sort || 0
        }));

        return {
          code: 0,
          data: {
            list: branchList,
            total: branchList.length
          },
          msg: '操作成功'
        };
      }

      // 如果API调用失败，尝试使用内容管理API
      return getBranchList();
    })
    .catch(error => {
      console.error('从后台获取分会列表失败:', error);
      // 如果API调用失败，尝试使用内容管理API
      return getBranchList();
    });
}

export default {
  getBranchList,
  getBackendBranchList,
  getBranchDetail
};

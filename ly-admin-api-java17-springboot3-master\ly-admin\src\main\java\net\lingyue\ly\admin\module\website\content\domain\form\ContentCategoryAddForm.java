package net.lingyue.ly.admin.module.website.content.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 内容分类添加表单
 */
@Data
public class ContentCategoryAddForm {

    @Schema(description = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    @Length(max = 50, message = "分类名称最多50字符")
    private String categoryName;

    @Schema(description = "父分类ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    @Length(max = 200, message = "备注最多200字符")
    private String remark;
}

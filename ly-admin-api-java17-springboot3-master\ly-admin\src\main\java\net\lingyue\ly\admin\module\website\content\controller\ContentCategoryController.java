package net.lingyue.ly.admin.module.website.content.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lingyue.ly.admin.constant.AdminSwaggerTagConst;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryAddForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryQueryForm;
import net.lingyue.ly.admin.module.website.content.domain.form.ContentCategoryUpdateForm;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentCategoryTreeVO;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentCategoryVO;
import net.lingyue.ly.admin.module.website.content.service.ContentCategoryService;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;

/**
 * 内容分类管理
 */
@Tag(name = AdminSwaggerTagConst.Website.CONTENT_CATEGORY)
@RestController
@OperateLog
public class ContentCategoryController {

    @Resource
    private ContentCategoryService contentCategoryService;

    @Operation(summary = "内容分类-添加 @author")
    @PostMapping("/website/contentCategory/add")
    @SaCheckPermission("website:content:category:add")
    public ResponseDTO<String> addContentCategory(@RequestBody @Valid ContentCategoryAddForm addForm) {
        return contentCategoryService.add(addForm);
    }

    @Operation(summary = "内容分类-修改 @author")
    @PostMapping("/website/contentCategory/update")
    @SaCheckPermission("website:content:category:update")
    public ResponseDTO<String> updateContentCategory(@RequestBody @Valid ContentCategoryUpdateForm updateForm) {
        return contentCategoryService.update(updateForm);
    }

    @Operation(summary = "内容分类-删除 @author")
    @GetMapping("/website/contentCategory/delete/{categoryId}")
    @SaCheckPermission("website:content:category:delete")
    public ResponseDTO<String> deleteContentCategory(@PathVariable Long categoryId) {
        return contentCategoryService.delete(categoryId);
    }

    @Operation(summary = "内容分类-详情 @author")
    @GetMapping("/website/contentCategory/get/{categoryId}")
    @SaCheckPermission("website:content:category:query")
    public ResponseDTO<ContentCategoryVO> getContentCategory(@PathVariable Long categoryId) {
        return contentCategoryService.getDetail(categoryId);
    }

    @Operation(summary = "内容分类-树形结构 @author")
    @PostMapping("/website/contentCategory/tree")
    @SaCheckPermission("website:content:category:query")
    public ResponseDTO<List<ContentCategoryTreeVO>> getContentCategoryTree(@RequestBody ContentCategoryQueryForm queryForm) {
        return contentCategoryService.queryTree(queryForm);
    }
}

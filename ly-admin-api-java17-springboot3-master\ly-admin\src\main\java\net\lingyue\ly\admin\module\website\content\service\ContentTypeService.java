package net.lingyue.ly.admin.module.website.content.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.module.website.content.dao.ContentDao;
import net.lingyue.ly.admin.module.website.content.dao.ContentTypeDao;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentEntity;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentTypeEntity;
import net.lingyue.ly.admin.module.website.content.domain.vo.ContentTypeVO;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 内容类型服务
 */
@Slf4j
@Service
public class ContentTypeService {

    @Resource
    private ContentTypeDao contentTypeDao;

    @Resource
    private ContentDao contentDao;

    /**
     * 获取所有内容类型
     *
     * @return 内容类型列表
     */
    public List<ContentTypeVO> getAll() {
        LambdaQueryWrapper<ContentTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ContentTypeEntity::getSort);
        List<ContentTypeEntity> contentTypeList = contentTypeDao.selectList(queryWrapper);
        return SmartBeanUtil.copyList(contentTypeList, ContentTypeVO.class);
    }

    /**
     * 添加内容类型
     *
     * @param contentTypeName 内容类型名称
     * @param contentTypeCode 内容类型编码
     * @param contentTypeDesc 内容类型描述
     * @param sort 排序
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(String contentTypeName, String contentTypeCode, String contentTypeDesc, Integer sort) {
        // 检查名称是否重复
        LambdaQueryWrapper<ContentTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTypeEntity::getContentTypeName, contentTypeName);
        if (contentTypeDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("内容类型名称已存在");
        }
        
        // 检查编码是否重复
        queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTypeEntity::getContentTypeCode, contentTypeCode);
        if (contentTypeDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("内容类型编码已存在");
        }
        
        ContentTypeEntity contentTypeEntity = new ContentTypeEntity();
        contentTypeEntity.setContentTypeName(contentTypeName);
        contentTypeEntity.setContentTypeCode(contentTypeCode);
        contentTypeEntity.setContentTypeDesc(contentTypeDesc);
        contentTypeEntity.setSort(sort);
        contentTypeEntity.setEnableFlag(true);
        
        LocalDateTime now = LocalDateTime.now();
        contentTypeEntity.setCreateTime(now);
        contentTypeEntity.setUpdateTime(now);
        
        contentTypeDao.insert(contentTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新内容类型
     *
     * @param contentTypeId 内容类型ID
     * @param contentTypeName 内容类型名称
     * @param contentTypeDesc 内容类型描述
     * @param sort 排序
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(Long contentTypeId, String contentTypeName, String contentTypeDesc, Integer sort) {
        ContentTypeEntity contentTypeEntity = contentTypeDao.selectById(contentTypeId);
        if (contentTypeEntity == null) {
            return ResponseDTO.userErrorParam("内容类型不存在");
        }
        
        // 检查名称是否重复
        if (!contentTypeEntity.getContentTypeName().equals(contentTypeName)) {
            LambdaQueryWrapper<ContentTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ContentTypeEntity::getContentTypeName, contentTypeName);
            if (contentTypeDao.selectCount(queryWrapper) > 0) {
                return ResponseDTO.userErrorParam("内容类型名称已存在");
            }
        }
        
        contentTypeEntity.setContentTypeName(contentTypeName);
        contentTypeEntity.setContentTypeDesc(contentTypeDesc);
        contentTypeEntity.setSort(sort);
        contentTypeEntity.setUpdateTime(LocalDateTime.now());
        
        contentTypeDao.updateById(contentTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 启用/禁用内容类型
     *
     * @param contentTypeId 内容类型ID
     * @param enableFlag 是否启用
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long contentTypeId, Boolean enableFlag) {
        ContentTypeEntity contentTypeEntity = contentTypeDao.selectById(contentTypeId);
        if (contentTypeEntity == null) {
            return ResponseDTO.userErrorParam("内容类型不存在");
        }
        
        contentTypeEntity.setEnableFlag(enableFlag);
        contentTypeEntity.setUpdateTime(LocalDateTime.now());
        
        contentTypeDao.updateById(contentTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 删除内容类型
     *
     * @param contentTypeId 内容类型ID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long contentTypeId) {
        ContentTypeEntity contentTypeEntity = contentTypeDao.selectById(contentTypeId);
        if (contentTypeEntity == null) {
            return ResponseDTO.userErrorParam("内容类型不存在");
        }
        
        // 检查是否有内容使用该类型
        LambdaQueryWrapper<ContentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentEntity::getContentTypeId, contentTypeId);
        if (contentDao.selectCount(queryWrapper) > 0) {
            return ResponseDTO.userErrorParam("该内容类型下有内容，无法删除");
        }
        
        contentTypeDao.deleteById(contentTypeId);
        return ResponseDTO.ok();
    }
}

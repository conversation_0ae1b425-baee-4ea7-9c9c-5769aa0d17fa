<!--
  * 文件上传组件
  *
  * @Author:    1024创新实验室
  * @Date:      2023-01-01
  * @Copyright  1024创新实验室
-->
<template>
  <div class="smart-upload">
    <a-upload
      :accept="props.accept"
      :multiple="props.multiple"
      :show-upload-list="false"
      :before-upload="beforeUpload"
      :customRequest="customRequest"
    >
      <a-button>
        <upload-outlined />
        上传
      </a-button>
    </a-upload>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '/@/api/support/file-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

const props = defineProps({
  limit: {
    type: Number,
    default: 1
  },
  fileType: {
    type: Number,
    default: 1 // 默认为通用类型
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  multiple: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['upload-success']);

// 上传前检查
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件必须小于10MB!');
    return false;
  }
  return true;
};

// 自定义上传
const customRequest = async (options) => {
  SmartLoading.show();
  try {
    const formData = new FormData();
    formData.append('file', options.file);
    console.log('开始上传文件:', options.file.name, '类型:', props.fileType);
    let res = await fileApi.uploadFile(formData, props.fileType);
    console.log('文件上传响应:', res);

    if (res.code === 0) {
      let file = res.data;
      // 确保文件对象有 url 属性
      if (file.fileUrl) {
        file.url = file.fileUrl;
      } else if (!file.url) {
        // 如果没有 url 和 fileUrl，尝试构建完整URL
        const baseUrl = import.meta.env.VITE_APP_API_URL || '';
        file.url = `${baseUrl}/support/file/getFile?fileKey=${file.fileKey}`;
        file.fileUrl = file.url;
      }

      file.name = file.fileName || options.file.name;

      console.log('处理后的文件对象:', file);
      emit('upload-success', [file]);
      message.success(`${options.file.name} 上传成功`);

      // 触发表单验证
      setTimeout(() => {
        const event = new Event('change', { bubbles: true });
        const inputElements = document.querySelectorAll('input[type="text"]');
        inputElements.forEach(input => {
          if (input.value && (input.value === file.url || input.value === file.fileUrl)) {
            input.dispatchEvent(event);
          }
        });
      }, 100);
    } else {
      message.error(res.msg || `${options.file.name} 上传失败`);
    }
  } catch (e) {
    console.error('文件上传错误:', e);
    smartSentry.captureError(e);
    message.error(`上传失败: ${e.message}`);
  } finally {
    SmartLoading.hide();
  }
};
</script>

<style lang="less" scoped>
.smart-upload {
  display: inline-block;
}
</style>

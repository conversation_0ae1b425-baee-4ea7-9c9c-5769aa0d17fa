<template>
  <div class="test-content-detail">
    <h2>ContentDetail 组件测试页面</h2>
    
    <div class="test-section">
      <h3>测试活动详情</h3>
      <ContentDetail 
        :detail="testActivityDetail" 
        :loading="false" 
        :config="testActivityConfig"
      />
    </div>
    
    <div class="test-section">
      <h3>测试分支机构详情</h3>
      <ContentDetail 
        :detail="testBranchDetail" 
        :loading="false" 
        :config="testBranchConfig"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import ContentDetail from '../components/common/ContentDetail.vue'

// 测试活动数据
const testActivityDetail = {
  id: 'test-001',
  title: '测试活动标题',
  time: '2025-01-01 10:00:00',
  location: '测试活动地点',
  organizer: '测试主办单位',
  contact: '测试联系方式',
  summary: '这是一个测试活动的摘要信息',
  contentHtml: '<p>这是测试活动的内容</p><p>包含HTML标签</p>',
  attachments: [
    { name: '测试附件1.pdf', url: '/files/test1.pdf' },
    { name: '测试附件2.doc', url: '/files/test2.doc' }
  ]
}

// 测试活动配置
const testActivityConfig = computed(() => ({
  idField: 'id',
  titleField: 'title',
  summaryField: 'summary',
  contentField: 'contentHtml',
  attachmentField: 'attachments',
  
  metaFields: [
    { key: 'time', label: '活动时间', type: 'datetime' },
    { key: 'location', label: '活动地点' },
    { key: 'organizer', label: '主办单位' },
    { key: 'contact', label: '联系方式' }
  ],
  
  infoSection: {
    fields: [
      { key: 'time', label: '活动时间', type: 'datetime' },
      { key: 'location', label: '活动地点' },
      { key: 'organizer', label: '主办单位' },
      { key: 'contact', label: '联系方式' }
    ]
  },
  
  summaryTitle: '活动简介',
  showActions: false,
  emptyText: '未找到活动信息',
  containerClass: 'test-activity-container'
}))

// 测试分支机构数据
const testBranchDetail = {
  id: 'test-branch-001',
  name: '测试分支机构',
  publishDate: '2025-01-01',
  source: '测试来源',
  author: '测试作者',
  viewCount: 100,
  director: '测试负责人',
  establishTime: '2024-01-01',
  contactPhone: '0371-12345678',
  address: '测试地址',
  email: '<EMAIL>',
  content: '<p>这是测试分支机构的内容</p>',
  attachments: []
}

// 测试分支机构配置
const testBranchConfig = computed(() => ({
  idField: 'id',
  titleField: 'name',
  contentField: 'content',
  attachmentField: 'attachments',
  
  metaFields: [
    { key: 'publishDate', label: '发布时间', type: 'date' },
    { key: 'source', label: '来源' },
    { key: 'author', label: '作者' },
    { key: 'viewCount', label: '浏览量' }
  ],
  
  infoSection: {
    fields: [
      { key: 'director', label: '负责人' },
      { key: 'establishTime', label: '成立时间' },
      { key: 'contactPhone', label: '联系电话' },
      { key: 'address', label: '办公地址' },
      { key: 'email', label: '电子邮箱' }
    ]
  },
  
  showActions: false,
  emptyText: '未找到分支机构信息',
  containerClass: 'test-branch-container'
}))
</script>

<style scoped>
.test-content-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-section {
  margin-bottom: 40px;
}

.test-section h3 {
  color: #333;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-activity-container,
.test-branch-container {
  background-color: #fff;
  margin: 20px 0;
}
</style>


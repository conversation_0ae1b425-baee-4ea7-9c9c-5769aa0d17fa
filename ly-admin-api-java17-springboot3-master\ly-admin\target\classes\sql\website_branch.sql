-- 分会表
CREATE TABLE IF NOT EXISTS `t_website_branch` (
  `branch_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分会ID',
  `branch_name` varchar(100) NOT NULL COMMENT '分会名称',
  `branch_code` varchar(50) NOT NULL COMMENT '分会编码',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `logo` varchar(255) DEFAULT NULL COMMENT 'Logo图片',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `disabled_flag` tinyint(1) DEFAULT '0' COMMENT '是否禁用',
  `deleted_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`branch_id`),
  UNIQUE KEY `uk_branch_code` (`branch_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分会表';

-- 为内容表添加分会ID字段
ALTER TABLE `t_website_content` 
ADD COLUMN `branch_id` bigint(20) DEFAULT NULL COMMENT '分会ID' AFTER `content_type_id`,
ADD INDEX `idx_branch_id` (`branch_id`);

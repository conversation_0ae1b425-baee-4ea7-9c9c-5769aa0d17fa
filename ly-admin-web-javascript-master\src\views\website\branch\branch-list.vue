<!--
  * 分会管理
-->
<template>
  <a-card :bordered="false">
    <a-row class="smart-query-form">
      <a-form layout="inline">
        <a-form-item label="分会名称">
          <a-input v-model:value="queryForm.branchName" placeholder="请输入分会名称" allowClear />
        </a-form-item>
        <a-form-item label="分会编码">
          <a-input v-model:value="queryForm.branchCode" placeholder="请输入分会编码" allowClear />
        </a-form-item>
        <a-form-item label="状态">
          <SmartBooleanSelect v-model:value="queryForm.disabledFlag" :defaultValue="undefined" :allowClear="true" />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="queryBranchList">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="resetQuery">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-row>

    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="showAddModal" v-privilege="'website:branch:add'">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
    </a-row>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="{
        total: total,
        current: queryForm.pageNum,
        pageSize: queryForm.pageSize,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条`,
        onChange: onPageChange,
        onShowSizeChange: onPageSizeChange,
      }"
      rowKey="branchId"
      size="middle"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'logo'">
          <a-image
            v-if="record.logo"
            :src="record.logo"
            :width="40"
            :height="40"
            :preview="{ src: record.logo }"
          />
          <span v-else>-</span>
        </template>
        <template v-else-if="column.dataIndex === 'disabledFlag'">
          <a-tag :color="record.disabledFlag ? 'red' : 'green'">
            {{ record.disabledFlag ? '禁用' : '启用' }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="showEditModal(record)" v-privilege="'website:branch:update'">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除该分会吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteBranch(record.branchId)"
              v-privilege="'website:branch:delete'"
            >
              <a class="smart-table-delete">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 分会表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="formData.branchId ? '编辑分会' : '新建分会'"
      @ok="handleOk"
      @cancel="handleCancel"
      :confirmLoading="confirmLoading"
      width="700px"
    >
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="分会名称" name="branchName">
          <a-input v-model:value="formData.branchName" placeholder="请输入分会名称" />
        </a-form-item>
        <a-form-item label="分会编码" name="branchCode">
          <a-input v-model:value="formData.branchCode" placeholder="请输入分会编码" />
        </a-form-item>
        <a-form-item label="Logo">
          <Upload
            :maxUploadSize="1"
            :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
            buttonText="上传Logo"
            listType="picture-card"
            :maxSize="2"
            :accept="'.jpg,.jpeg,.png'"
            @change="changeLogo"
            :defaultFileList="logoFileList"
          />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formData.sort" :min="0" :max="9999" style="width: 100%" />
        </a-form-item>
        <a-form-item label="是否禁用">
          <a-switch v-model:checked="formData.disabledFlag" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { PAGE_SIZE } from '/@/constants/common-const';
import SmartBooleanSelect from '/@/components/framework/boolean-select/index.vue';
import Upload from '/@/components/support/file-upload/index.vue';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import { queryBranch, addBranch, updateBranch, deleteBranch as deleteBranchApi } from '/@/api/website/branch';

// 表格列定义
const columns = [
  {
    title: 'Logo',
    dataIndex: 'logo',
    width: 80,
  },
  {
    title: '分会名称',
    dataIndex: 'branchName',
    width: 200,
  },
  {
    title: '分会编码',
    dataIndex: 'branchCode',
    width: 150,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'disabledFlag',
    width: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150,
    fixed: 'right',
  },
];

// 查询表单
const queryForm = reactive({
  branchName: '',
  branchCode: '',
  disabledFlag: undefined,
  pageNum: 1,
  pageSize: PAGE_SIZE,
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

// 表单数据
const formRef = ref();
const modalVisible = ref(false);
const confirmLoading = ref(false);
const logoFileList = ref([]);

const formData = reactive({
  branchId: undefined,
  branchName: '',
  branchCode: '',
  logo: '',
  sort: 0,
  disabledFlag: false,
  description: '',
});

const defaultFormData = {
  branchId: undefined,
  branchName: '',
  branchCode: '',
  logo: '',
  sort: 0,
  disabledFlag: false,
  description: '',
};

// 表单验证规则
const rules = {
  branchName: [
    { required: true, message: '请输入分会名称', trigger: 'blur' },
    { max: 50, message: '分会名称最多50个字符', trigger: 'blur' },
  ],
  branchCode: [
    { required: true, message: '请输入分会编码', trigger: 'blur' },
    { max: 30, message: '分会编码最多30个字符', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
  ],
  description: [
    { max: 500, message: '描述最多500个字符', trigger: 'blur' },
  ],
};

// 查询分会列表
async function queryBranchList() {
  loading.value = true;
  try {
    const res = await queryBranch(queryForm);
    if (res.code === 0 && res.data) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    } else {
      tableData.value = [];
      total.value = 0;
      message.error(res.msg || '获取分会列表失败');
    }
  } catch (error) {
    console.error('获取分会列表失败:', error);
    message.error('获取分会列表失败');
  } finally {
    loading.value = false;
  }
}

// 重置查询条件
function resetQuery() {
  queryForm.branchName = '';
  queryForm.branchCode = '';
  queryForm.disabledFlag = undefined;
  queryForm.pageNum = 1;
  queryBranchList();
}

// 分页变化
function onPageChange(page, pageSize) {
  queryForm.pageNum = page;
  queryForm.pageSize = pageSize;
  queryBranchList();
}

// 每页条数变化
function onPageSizeChange(current, size) {
  queryForm.pageNum = 1;
  queryForm.pageSize = size;
  queryBranchList();
}

// 显示新增弹窗
function showAddModal() {
  Object.assign(formData, defaultFormData);
  logoFileList.value = [];
  modalVisible.value = true;
}

// 显示编辑弹窗
function showEditModal(record) {
  Object.assign(formData, record);
  
  // 处理Logo
  if (record.logo) {
    logoFileList.value = [{
      fileName: '分会Logo',
      fileType: record.logo.toLowerCase().endsWith('.png') ? 'png' : 'jpg',
      fileUrl: record.logo,
      url: record.logo,
      folderType: FILE_FOLDER_TYPE_ENUM.COMMON.value
    }];
  } else {
    logoFileList.value = [];
  }
  
  modalVisible.value = true;
}

// 删除分会
async function deleteBranch(branchId) {
  try {
    const res = await deleteBranchApi(branchId);
    if (res.code === 0) {
      message.success('删除成功');
      queryBranchList();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (error) {
    console.error('删除分会失败:', error);
    message.error('删除分会失败');
  }
}

// 表单提交
async function handleOk() {
  try {
    confirmLoading.value = true;
    
    // 表单验证
    await formRef.value.validate();
    
    // 提交表单
    let res;
    if (formData.branchId) {
      res = await updateBranch(formData);
    } else {
      res = await addBranch(formData);
    }
    
    if (res.code === 0) {
      message.success(formData.branchId ? '更新成功' : '添加成功');
      modalVisible.value = false;
      queryBranchList();
    } else {
      message.error(res.msg || (formData.branchId ? '更新失败' : '添加失败'));
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    message.error('表单验证失败，请检查输入');
  } finally {
    confirmLoading.value = false;
  }
}

// 取消表单
function handleCancel() {
  modalVisible.value = false;
}

// 上传Logo
function changeLogo(fileList) {
  if (fileList && fileList.length > 0) {
    const file = fileList[0];
    formData.logo = file.fileUrl || file.url;
  } else {
    formData.logo = '';
  }
}

// 页面加载时获取数据
onMounted(() => {
  queryBranchList();
});
</script>

<style lang="less" scoped>
.smart-table-delete {
  color: #ff4d4f;
}
</style>

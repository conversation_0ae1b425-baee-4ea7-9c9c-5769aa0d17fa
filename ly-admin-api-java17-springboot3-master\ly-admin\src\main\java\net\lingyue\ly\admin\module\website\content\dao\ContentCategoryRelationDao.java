package net.lingyue.ly.admin.module.website.content.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lingyue.ly.admin.module.website.content.domain.entity.ContentCategoryRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容分类关联DAO
 */
@Mapper
public interface ContentCategoryRelationDao extends BaseMapper<ContentCategoryRelationEntity> {

    /**
     * 根据内容ID查询分类ID列表
     *
     * @param contentId 内容ID
     * @return 分类ID列表
     */
    List<Long> selectCategoryIdsByContentId(@Param("contentId") Long contentId);

    /**
     * 根据分类ID查询内容ID列表
     *
     * @param categoryId 分类ID
     * @return 内容ID列表
     */
    List<Long> selectContentIdsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 批量插入内容分类关联
     *
     * @param contentId 内容ID
     * @param categoryIds 分类ID列表
     * @return 影响行数
     */
    int batchInsert(@Param("contentId") Long contentId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 根据内容ID删除关联
     *
     * @param contentId 内容ID
     * @return 影响行数
     */
    int deleteByContentId(@Param("contentId") Long contentId);
}

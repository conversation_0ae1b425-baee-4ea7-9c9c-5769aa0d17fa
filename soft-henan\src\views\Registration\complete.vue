<template>
  <div class="complete-container">
    <div class="complete-header">
      <img src="/success-icon.png" alt="成功" class="success-icon" />
      <h1>报名成功</h1>
      <p class="subtitle">感谢您报名参加河南省软组织病研究会会议</p>
    </div>
    
    <div class="registration-card">
      <h2>报名信息</h2>
      <div class="mobile-info-list">
        <div class="info-item">
          <div class="info-label">姓名</div>
          <div class="info-value">{{ registrationData.name }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">手机号码</div>
          <div class="info-value">{{ registrationData.phone }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">医院/单位</div>
          <div class="info-value">{{ registrationData.hospital }}</div>
        </div>
        <div class="info-item" v-if="registrationData.title">
          <div class="info-label">职称/职务</div>
          <div class="info-value">{{ registrationData.title }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">参会人数</div>
          <div class="info-value">{{ registrationData.attendees }}人</div>
        </div>
        <div class="info-item">
          <div class="info-label">参会时间</div>
          <div class="info-value">{{ registrationData.time }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">是否参加晚宴</div>
          <div class="info-value">{{ registrationData.dinner ? '是' : '否' }}</div>
        </div>
        <div class="info-item" v-if="registrationData.specialNeeds">
          <div class="info-label">特殊需求</div>
          <div class="info-value">{{ registrationData.specialNeeds }}</div>
        </div>
        <div class="info-item" v-if="registrationData.needInvoice">
          <div class="info-label">发票信息</div>
          <div class="info-value">
            抬头: {{ registrationData.invoiceTitle }}
            <template v-if="registrationData.taxNumber">
              <br>税号: {{ registrationData.taxNumber }}
            </template>
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">支付金额</div>
          <div class="info-value fee-amount">¥{{ registrationData.totalFee.toFixed(2) }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">订单号</div>
          <div class="info-value order-id">{{ registrationData.orderId }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">支付方式</div>
          <div class="info-value">{{ paymentData.method === 'wechat' ? '微信支付' : '支付宝' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">支付时间</div>
          <div class="info-value">{{ formatDateTime(paymentData.time) }}</div>
        </div>
      </div>
    </div>
    
    <div class="notice-card">
      <h2>温馨提示</h2>
      <a-alert 
        type="info" 
        show-icon
        message="请保存好您的报名信息"
        description="会议前我们将通过短信提醒您参会，您也可以截图保存此页面作为凭证。如有任何问题，请联系组委会。"
      />
      
      <div class="meeting-info">
        <h3>会议信息</h3>
        <div class="meeting-details">
          <p><i class="icon icon-time"></i> <strong>会议时间：</strong>2024年X月X日 9:00-17:00</p>
          <p><i class="icon icon-location"></i> <strong>会议地点：</strong>郑州市XXXX酒店（XXXX路XX号）</p>
          <p><i class="icon icon-phone"></i> <strong>联系电话：</strong>0371-XXXXXXXX（赵老师）</p>
        </div>
      </div>
      
      <div class="action-buttons">
        <a-button type="primary" @click="backToHome" size="large" block>返回首页</a-button>
        <a-button @click="downloadInfo" size="large" block>保存信息</a-button>
      </div>
    </div>
    
    <div class="contact-info">
      <p><strong>主办单位：</strong>河南省软组织病研究会</p>
      <p><strong>联系方式：</strong>0371-XXXXXXXX（赵老师）</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

const router = useRouter()
const registrationData = reactive({})
const paymentData = reactive({})

onMounted(() => {
  // 从本地存储获取报名和支付数据
  const storedRegistration = localStorage.getItem('registrationData')
  const storedPayment = localStorage.getItem('paymentData')
  
  if (storedRegistration && storedPayment) {
    Object.assign(registrationData, JSON.parse(storedRegistration))
    Object.assign(paymentData, JSON.parse(storedPayment))

    // 这里可以调用后端API保存报名信息
    // saveRegistrationToServer()
  } else {
    message.error('未找到报名或支付信息')
    router.push('/registration')
  }
})

const formatDateTime = (timeString) => {
  if (!timeString) return ''
  return dayjs(timeString).format('YYYY-MM-DD HH:mm:ss')
}

const backToHome = () => {
  // 清除本地存储的报名和支付数据
  localStorage.removeItem('registrationData')
  localStorage.removeItem('paymentData')
  
  // 返回首页
  router.push('/')
}

const downloadInfo = () => {
  message.success('报名信息已保存到您的相册')
  // 实际实现可以是导出PDF或生成图片
  // 也可以调用原生能力保存到相册
}

// 模拟将报名信息保存到服务器
const saveRegistrationToServer = () => {
  // 这里应该是向后端API发送请求的代码
  console.log('保存报名信息到服务器', {
    ...registrationData,
    payment: paymentData
  })
}
</script>

<style scoped>
.complete-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 15px;
}

.complete-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 30px 0;
}

.success-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

h1 {
  color: #52c41a;
  margin-bottom: 8px;
  font-size: 22px;
}

.subtitle {
  color: #666;
  font-size: 14px;
}

h2 {
  color: #1890ff;
  margin-bottom: 16px;
  font-size: 18px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.registration-card, 
.notice-card {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

/* 移动端信息列表 */
.mobile-info-list {
  display: flex;
  flex-direction: column;
}

.info-item {
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  flex-direction: column;
}

.info-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 4px;
}

.info-value {
  color: #333;
  font-size: 16px;
  word-break: break-all;
}

.fee-amount {
  font-size: 18px;
  font-weight: bold;
  color: #f5222d;
}

.order-id {
  color: #666;
  font-family: monospace;
  font-size: 14px;
}

.meeting-info {
  margin-top: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

h3 {
  font-size: 16px;
  margin-bottom: 14px;
  color: #333;
}

.meeting-details {
  color: #666;
  line-height: 1.8;
  font-size: 14px;
}

.icon {
  display: inline-block;
  margin-right: 8px;
  font-style: normal;
  color: #1890ff;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-info {
  text-align: center;
  color: #666;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  line-height: 1.8;
  font-size: 14px;
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .complete-container {
    padding: 10px;
  }

  h1 {
    font-size: 20px;
  }

  h2 {
    font-size: 16px;
  }

  .complete-header {
    padding: 20px 0;
  }

  .success-icon {
    width: 56px;
    height: 56px;
  }

  .registration-card,
  .notice-card {
    padding: 12px;
  }
}

@media (min-width: 768px) {
  .info-item {
    flex-direction: row;
    align-items: center;
  }
  
  .info-label {
    width: 100px;
    margin-bottom: 0;
  }
  
  .info-value {
    flex: 1;
  }
  
  .action-buttons {
    flex-direction: row;
    justify-content: center;
  }
  
  .action-buttons button {
    min-width: 150px;
  }
}
</style> 
package net.lingyue.ly.admin.module.website.content.domain.form;

import lombok.Data;
import net.lingyue.ly.base.common.domain.PageParam;

/**
 * 内容查询表单
 */
@Data
public class ContentQueryForm extends PageParam {

    /**
     * 内容类型ID
     */
    private Long contentTypeId;

    /**
     * 分会ID
     */
    private Long branchId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 关键字（标题、作者、来源）
     */
    private String keywords;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 是否删除
     */
    private Boolean deletedFlag;

    /**
     * 状态：0-草稿，1-已发布，2-已下线
     */
    private Integer status;

    /**
     * 是否置顶
     */
    private Boolean topFlag;

    /**
     * 是否推荐
     */
    private Boolean recommendFlag;

    /**
     * 发布开始时间
     */
    private String publishTimeBegin;

    /**
     * 发布结束时间
     */
    private String publishTimeEnd;

    /**
     * 创建开始时间
     */
    private String createTimeBegin;

    /**
     * 创建结束时间
     */
    private String createTimeEnd;
}
